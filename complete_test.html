<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整测试 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 简化的样式，确保页面正常显示 */
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto; white-space: pre-wrap; margin: 10px 0; }
        .hidden { display: none; }
        .file-box { border: 2px dashed #bdc3c7; padding: 20px; text-align: center; margin: 10px 0; background: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜篮子智能比价工具 - 完整测试</h1>
        
        <!-- 测试控制区 -->
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="testPriceParser()">测试价格解析</button>
            <button class="test-button" onclick="testDataLoading()">测试数据加载</button>
            <button class="test-button" onclick="clearTestConsole()">清空控制台</button>
        </div>
        
        <!-- 测试控制台 -->
        <div class="test-section">
            <h3>测试控制台</h3>
            <div id="testConsole" class="console"></div>
        </div>
        
        <!-- 主要功能区域 -->
        <div class="upload-section">
            <div class="file-upload-container">
                <div class="file-upload-box">
                    <h3>销售价目表</h3>
                    <div id="salesFileBox" class="file-box">
                        <p>点击选择文件或拖拽文件到此处</p>
                        <input type="file" id="salesFile" accept=".xlsx,.xls" class="hidden">
                    </div>
                    <div id="salesFileInfo" class="file-info"></div>
                </div>
                
                <div class="file-upload-box">
                    <h3>菜篮子价格表</h3>
                    <div id="basketFileBox" class="file-box">
                        <p>点击选择文件或拖拽文件到此处</p>
                        <input type="file" id="basketFile" accept=".xlsx,.xls" class="hidden">
                    </div>
                    <div id="basketFileInfo" class="file-info"></div>
                </div>
            </div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button id="compareBtn" class="btn btn-primary">开始比价</button>
            <button id="clearBtn" class="btn btn-secondary">清空数据</button>
            <button id="exportBtn" class="btn btn-success">导出结果</button>
            <button id="retryBtn" class="btn btn-warning">重试</button>
            <button id="helpBtn" class="btn btn-info">帮助</button>
            <button id="demoBtn" class="btn btn-demo">加载演示数据</button>
        </div>
        
        <!-- 进度显示 -->
        <div id="progressSection" class="progress-section hidden">
            <div class="progress-bar">
                <div id="progressBar" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">准备中...</div>
        </div>
        
        <!-- 错误显示 -->
        <div id="errorSection" class="error-section hidden">
            <div class="error-content">
                <h3>处理出错</h3>
                <p id="errorMessage"></p>
                <button id="errorRetryBtn" class="btn btn-primary">重试</button>
            </div>
        </div>
        
        <!-- 结果显示 -->
        <div id="resultsSection" class="results-section hidden">
            <div class="results-summary">
                <div class="summary-item">
                    <span class="summary-label">匹配商品数量：</span>
                    <span id="totalMatched" class="summary-value">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">平均价差：</span>
                    <span id="avgPriceDiff" class="summary-value">0%</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">匹配日期：</span>
                    <span id="matchedDate" class="summary-value">-</span>
                </div>
            </div>
            
            <div class="results-table-container">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>商品名称</th>
                            <th>销售价格</th>
                            <th>菜篮子价格</th>
                            <th>价差</th>
                            <th>价差百分比</th>
                            <th>匹配度</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 帮助模态框 -->
        <div id="helpModal" class="modal hidden">
            <div class="modal-content">
                <span class="close-help">&times;</span>
                <h2>使用帮助</h2>
                <div class="help-content">
                    <p>这是帮助内容...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
    <script>
        // 测试专用函数
        function testLog(message) {
            const console = document.getElementById('testConsole');
            console.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.scrollTop = console.scrollHeight;
        }
        
        function clearTestConsole() {
            document.getElementById('testConsole').textContent = '';
        }
        
        function testPriceParser() {
            testLog('=== 开始测试价格解析功能 ===');
            
            // 检查是否有PriceComparisonTool实例
            if (typeof window.priceComparisonTool === 'undefined') {
                testLog('错误：PriceComparisonTool实例未找到');
                return;
            }
            
            const tool = window.priceComparisonTool;
            
            // 测试用例
            const testCases = [
                { input: '12.50', expected: 12.50 },
                { input: 12.50, expected: 12.50 },
                { input: '￥15.80', expected: 15.80 },
                { input: '20元', expected: 20 },
                { input: '25.00元/斤', expected: 25.00 },
                { input: '无', expected: null },
                { input: '缺', expected: null },
                { input: '--', expected: null },
                { input: '', expected: null },
                { input: null, expected: null }
            ];
            
            let passedTests = 0;
            testCases.forEach((testCase, index) => {
                const result = tool.parsePrice(testCase.input);
                const passed = result === testCase.expected;
                testLog(`测试 ${index + 1}: 输入="${testCase.input}" 期望=${testCase.expected} 实际=${result} ${passed ? '✓' : '✗'}`);
                if (passed) passedTests++;
            });
            
            testLog(`=== 价格解析测试完成: ${passedTests}/${testCases.length} 通过 ===`);
        }
        
        async function testDataLoading() {
            testLog('=== 开始测试数据加载功能 ===');
            
            try {
                // 测试文件是否存在
                const salesFile = '2025年7月销售价目表2025.xlsx';
                const basketFile = '广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx';
                
                testLog('检查销售价目表文件...');
                const salesResponse = await fetch(salesFile);
                if (salesResponse.ok) {
                    testLog('✓ 销售价目表文件存在');
                } else {
                    testLog('✗ 销售价目表文件不存在或无法访问');
                }
                
                testLog('检查菜篮子价格表文件...');
                const basketResponse = await fetch(basketFile);
                if (basketResponse.ok) {
                    testLog('✓ 菜篮子价格表文件存在');
                } else {
                    testLog('✗ 菜篮子价格表文件不存在或无法访问');
                }
                
                testLog('=== 数据加载测试完成 ===');
                
            } catch (error) {
                testLog('数据加载测试出错: ' + error.message);
            }
        }
        
        // 在页面加载完成后设置全局引用
        document.addEventListener('DOMContentLoaded', () => {
            // 等待一段时间确保PriceComparisonTool初始化完成
            setTimeout(() => {
                if (typeof PriceComparisonTool !== 'undefined') {
                    // 尝试获取已创建的实例或创建新实例
                    if (!window.priceComparisonTool) {
                        window.priceComparisonTool = new PriceComparisonTool();
                    }
                    testLog('PriceComparisonTool 实例已准备就绪');
                } else {
                    testLog('警告：PriceComparisonTool 类未找到');
                }
            }, 1000);
        });
    </script>
</body>
</html>
