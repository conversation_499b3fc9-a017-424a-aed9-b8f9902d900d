<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速调试工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 600px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>快速调试 - 菜篮子价格问题</h1>
        
        <div>
            <input type="file" id="basketFile" accept=".xlsx,.xls">
            <button class="btn" onclick="testBasketFile()">测试菜篮子文件</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div id="logSection" class="log-section"></div>
    </div>

    <script>
        let basketData = null;

        function log(message) {
            const logSection = document.getElementById('logSection');
            const timestamp = new Date().toLocaleTimeString();
            logSection.textContent += `[${timestamp}] ${message}\n`;
            logSection.scrollTop = logSection.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logSection').textContent = '';
        }

        document.getElementById('basketFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`正在读取文件: ${file.name}`);
                readBasketFile(file);
            }
        });

        function readBasketFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { 
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false,
                        raw: false,
                        defval: ''
                    });
                    
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // 使用数组格式解析
                    const rawData = XLSX.utils.sheet_to_json(worksheet, {
                        header: 1,
                        defval: '',
                        blankrows: false,
                        raw: false
                    });
                    
                    log(`原始数据行数: ${rawData.length}`);
                    log(`前5行原始数据:`);
                    rawData.slice(0, 5).forEach((row, index) => {
                        log(`  行${index}: [${row.slice(0, 10).map(cell => `"${cell}"`).join(', ')}]`);
                    });
                    
                    basketData = parseBasketPriceTable(rawData);
                    log(`解析完成，数据行数: ${basketData.length}`);
                    
                } catch (error) {
                    log(`读取文件失败: ${error.message}`);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function parseBasketPriceTable(rawData) {
            log('\n=== 开始解析菜篮子价格表 ===');
            
            let basicHeaderRow = null;
            let dateHeaderRow = null;
            let priceTypeRow = null;
            let dataStartRow = 4;

            // 查找基本表头行
            for (let i = 0; i < Math.min(rawData.length, 5); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const hasBasicFields = row.some(cell => 
                        cell && typeof cell === 'string' && 
                        (cell.includes('商品/分类名称') || cell.includes('商品名称') || 
                         cell.includes('计价单位') || cell.includes('规格等级'))
                    );
                    
                    if (hasBasicFields) {
                        basicHeaderRow = row;
                        log(`找到基本表头行 ${i}: [${row.slice(0, 10).map(cell => `"${cell}"`).join(', ')}]`);
                        
                        if (i + 1 < rawData.length) {
                            const nextRow = rawData[i + 1];
                            if (nextRow && nextRow.some(cell => 
                                cell && cell.toString().includes('2025年'))) {
                                dateHeaderRow = nextRow;
                                log(`找到日期表头行 ${i + 1}: [${nextRow.slice(0, 10).map(cell => `"${cell}"`).join(', ')}]`);
                            }
                        }
                        
                        if (i + 2 < rawData.length) {
                            const priceRow = rawData[i + 2];
                            if (priceRow && priceRow.some(cell => 
                                cell && (cell.toString().includes('本期') || cell.toString().includes('上期')))) {
                                priceTypeRow = priceRow;
                                dataStartRow = i + 3;
                                log(`找到价格类型行 ${i + 2}: [${priceRow.slice(0, 10).map(cell => `"${cell}"`).join(', ')}]`);
                            }
                        }
                        break;
                    }
                }
            }

            if (!basicHeaderRow) {
                log('未找到基本表头行，使用默认处理');
                basicHeaderRow = rawData[0] || [];
            }

            // 构建合并的表头
            const maxColumns = Math.max(...rawData.map(row => row ? row.length : 0));
            log(`最大列数: ${maxColumns}`);
            
            const mergedHeaders = new Array(maxColumns).fill('');

            // 处理基本字段列
            if (basicHeaderRow) {
                for (let i = 0; i < Math.min(4, basicHeaderRow.length); i++) {
                    const cell = basicHeaderRow[i];
                    if (cell && cell.toString().trim() !== '') {
                        mergedHeaders[i] = cell.toString().trim();
                        log(`基本字段列${i}: "${mergedHeaders[i]}"`);
                    }
                }
            }

            // 处理日期相关的列
            if (dateHeaderRow && priceTypeRow) {
                log('\n=== 开始合并日期和价格类型列 ===');
                
                for (let i = 4; i < maxColumns; i++) {
                    const dateCell = dateHeaderRow[i];
                    const priceTypeCell = priceTypeRow[i];
                    
                    let columnName = '';
                    
                    if (dateCell && dateCell.toString().trim() !== '') {
                        const dateStr = dateCell.toString().trim();
                        if (priceTypeCell && priceTypeCell.toString().trim() !== '') {
                            const priceType = priceTypeCell.toString().trim();
                            columnName = `${dateStr}-${priceType}`;
                        } else {
                            columnName = dateStr;
                        }
                    } else if (priceTypeCell && priceTypeCell.toString().trim() !== '') {
                        columnName = priceTypeCell.toString().trim();
                        
                        // 如果是"本期"，尝试关联前面的日期
                        if (columnName === '本期') {
                            for (let j = i - 1; j >= Math.max(4, i - 3); j--) {
                                const prevDateCell = dateHeaderRow[j];
                                if (prevDateCell && prevDateCell.toString().includes('2025年')) {
                                    columnName = `${prevDateCell.toString().trim()}-本期`;
                                    log(`关联本期列${i}到日期: ${columnName}`);
                                    break;
                                }
                            }
                        }
                    } else {
                        columnName = `列${i + 1}`;
                    }
                    
                    mergedHeaders[i] = columnName;
                    if (columnName.includes('本期') || columnName.includes('上期')) {
                        log(`价格列${i}: "${columnName}"`);
                    }
                }
            }

            log(`\n最终表头: [${mergedHeaders.slice(0, 10).map(h => `"${h}"`).join(', ')}...]`);

            // 处理数据行
            const dataRows = rawData.slice(dataStartRow);
            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    mergedHeaders.forEach((header, index) => {
                        obj[header] = (row[index] !== null && row[index] !== undefined) ? row[index].toString() : '';
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && val.toString().trim().length > 0);
                    return hasContent;
                });

            log(`\n处理完成，数据行数: ${processedData.length}`);
            
            // 显示前3行数据样本
            log('\n前3行数据样本:');
            processedData.slice(0, 3).forEach((row, index) => {
                const keys = Object.keys(row);
                log(`  行${index + 1}: 商品="${row[keys[0]]}", 本期价格="${row[keys.find(k => k.includes('本期')) || 'N/A']}"`);
            });
            
            return processedData;
        }

        function testBasketFile() {
            if (!basketData) {
                log('请先上传菜篮子价格表文件');
                return;
            }

            log('\n=== 测试日期列识别 ===');
            const columns = Object.keys(basketData[0]);
            log(`所有列名: [${columns.slice(0, 10).map(c => `"${c}"`).join(', ')}...]`);
            
            // 查找本期列
            const benqiColumns = columns.filter(col => col.includes('本期'));
            log(`找到的本期列: [${benqiColumns.map(c => `"${c}"`).join(', ')}]`);
            
            if (benqiColumns.length > 0) {
                const bestColumn = benqiColumns[0];
                log(`选择的最佳本期列: "${bestColumn}"`);
                
                // 测试价格读取
                log('\n=== 测试价格读取 ===');
                basketData.slice(0, 5).forEach((row, index) => {
                    const productName = row[columns[0]];
                    const price = row[bestColumn];
                    log(`  商品${index + 1}: "${productName}" - 本期价格: "${price}"`);
                });
            } else {
                log('❌ 未找到任何本期列！');
            }
        }
    </script>
</body>
</html>
