#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构的脚本
"""

import pandas as pd
import openpyxl
import sys
import os

def check_excel_file(file_path):
    """检查Excel文件的详细结构"""
    print(f"检查文件: {file_path}")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return
    
    try:
        # 使用openpyxl检查工作表
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        print(f"工作表数量: {len(workbook.sheetnames)}")
        print(f"工作表名称: {workbook.sheetnames}")
        print()
        
        for sheet_name in workbook.sheetnames:
            print(f"工作表: {sheet_name}")
            print("-" * 30)
            
            sheet = workbook[sheet_name]
            print(f"最大行数: {sheet.max_row}")
            print(f"最大列数: {sheet.max_column}")
            print(f"使用范围: {sheet.calculate_dimension()}")
            
            # 显示前5行的内容
            print("前5行内容:")
            for row in range(1, min(6, sheet.max_row + 1)):
                row_data = []
                for col in range(1, min(11, sheet.max_column + 1)):  # 最多显示10列
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value is not None else "")
                print(f"  行{row}: {row_data}")
            
            print()
        
        # 使用pandas检查
        print("使用pandas读取:")
        print("-" * 30)
        
        # 尝试读取所有工作表
        excel_data = pd.read_excel(file_path, sheet_name=None)
        
        for sheet_name, df in excel_data.items():
            print(f"工作表: {sheet_name}")
            print(f"  形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            print(f"  前3行:")
            print(df.head(3).to_string(index=False))
            print()
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    # 检查当前目录下的Excel文件
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 查找Excel文件
    excel_files = []
    for file in os.listdir(current_dir):
        if file.endswith(('.xlsx', '.xls')):
            excel_files.append(file)
    
    print(f"找到的Excel文件: {excel_files}")
    print()
    
    if excel_files:
        for excel_file in excel_files:
            check_excel_file(excel_file)
            print("\n" + "=" * 80 + "\n")
    else:
        print("未找到Excel文件")
