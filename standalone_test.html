<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立测试 - 菜篮子比价工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .test-button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .test-button:hover { background: #0056b3; }
        .test-button:disabled { background: #6c757d; cursor: not-allowed; }
        .console { background: #1a1a1a; color: #00ff00; padding: 15px; font-family: 'Courier New', monospace; height: 300px; overflow-y: auto; white-space: pre-wrap; margin: 10px 0; border-radius: 5px; font-size: 13px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #333; text-align: center; }
        h3 { color: #555; margin-bottom: 10px; }
        .test-result { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 菜篮子智能比价工具 - 独立功能测试</h1>
        
        <div class="test-section">
            <h3>📋 测试控制</h3>
            <button class="test-button" onclick="runPriceParserTest()">🔢 测试价格解析</button>
            <button class="test-button" onclick="runStringMatchTest()">🔍 测试字符串匹配</button>
            <button class="test-button" onclick="runAllTests()">🚀 运行所有测试</button>
            <button class="test-button" onclick="clearConsole()">🧹 清空控制台</button>
        </div>
        
        <div id="status" class="status info">准备运行测试...</div>
        
        <div class="test-section">
            <h3>📊 测试控制台</h3>
            <div id="console" class="console">等待测试开始...\n</div>
        </div>
    </div>
    
    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            console.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = message;
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
            setStatus('控制台已清空', 'info');
        }
        
        // 独立的价格解析函数（从script.js复制并简化）
        function parsePrice(priceStr) {
            log(`解析价格: "${priceStr}" (类型: ${typeof priceStr})`);
            
            if (priceStr === null || priceStr === undefined || priceStr === '') {
                log(`返回 null: 输入为空`, 'warning');
                return null;
            }

            // 如果已经是数字，直接返回
            if (typeof priceStr === 'number') {
                log(`输入已是数字: ${priceStr}`, 'success');
                return isNaN(priceStr) ? null : priceStr;
            }

            // 转换为字符串
            let priceString = String(priceStr).trim();
            log(`字符串化: "${priceString}"`);
            
            // 处理特殊情况：如果包含"无"、"缺"、"-"等表示无价格的字符
            if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
                log(`识别为无价格标识: "${priceString}"`, 'warning');
                return null;
            }
            
            // 移除货币符号、单位等非数字字符，但保留数字、小数点和负号
            const cleanPrice = priceString.replace(/[^\d.-]/g, '');
            log(`清理后: "${cleanPrice}"`);
            
            // 如果清理后为空，返回null
            if (!cleanPrice) {
                log(`清理后为空`, 'warning');
                return null;
            }
            
            const price = parseFloat(cleanPrice);
            log(`解析结果: ${price} (isNaN: ${isNaN(price)})`, isNaN(price) ? 'error' : 'success');

            return isNaN(price) ? null : price;
        }
        
        // 简化的字符串匹配函数
        function calculateSimilarity(str1, str2) {
            if (!str1 || !str2) return 0;
            
            str1 = str1.toLowerCase().trim();
            str2 = str2.toLowerCase().trim();
            
            if (str1 === str2) return 1;
            
            // 简单的相似度计算
            const longer = str1.length > str2.length ? str1 : str2;
            const shorter = str1.length > str2.length ? str2 : str1;
            
            if (longer.length === 0) return 1;
            
            const editDistance = levenshteinDistance(longer, shorter);
            return (longer.length - editDistance) / longer.length;
        }
        
        function levenshteinDistance(str1, str2) {
            const matrix = [];
            
            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }
            
            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }
            
            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }
            
            return matrix[str2.length][str1.length];
        }
        
        // 价格解析测试
        function runPriceParserTest() {
            log('=== 开始价格解析测试 ===', 'info');
            setStatus('正在运行价格解析测试...', 'info');
            
            const testCases = [
                { input: '12.50', expected: 12.50, description: '标准小数' },
                { input: 12.50, expected: 12.50, description: '数字类型' },
                { input: '￥15.80', expected: 15.80, description: '带货币符号' },
                { input: '20元', expected: 20, description: '带单位' },
                { input: '25.00元/斤', expected: 25.00, description: '带复杂单位' },
                { input: '30.5', expected: 30.5, description: '简单小数' },
                { input: '无', expected: null, description: '无价格标识' },
                { input: '缺', expected: null, description: '缺货标识' },
                { input: '--', expected: null, description: '横线标识' },
                { input: '', expected: null, description: '空字符串' },
                { input: null, expected: null, description: 'null值' },
                { input: undefined, expected: null, description: 'undefined值' },
                { input: '0', expected: 0, description: '零价格' },
                { input: '0.00', expected: 0, description: '零价格小数' },
                { input: 'N/A', expected: null, description: 'N/A标识' },
                { input: '45.67元/公斤', expected: 45.67, description: '复杂单位' }
            ];
            
            let passedTests = 0;
            let totalTests = testCases.length;
            
            testCases.forEach((testCase, index) => {
                log(`\n--- 测试 ${index + 1}: ${testCase.description} ---`);
                const result = parsePrice(testCase.input);
                const passed = result === testCase.expected;
                
                log(`输入: ${JSON.stringify(testCase.input)}`);
                log(`期望: ${testCase.expected}`);
                log(`实际: ${result}`);
                log(`结果: ${passed ? '通过' : '失败'}`, passed ? 'success' : 'error');
                
                if (passed) passedTests++;
            });
            
            log(`\n=== 价格解析测试完成 ===`, 'info');
            log(`通过: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`, passedTests === totalTests ? 'success' : 'warning');
            
            setStatus(`价格解析测试完成: ${passedTests}/${totalTests} 通过`, passedTests === totalTests ? 'success' : 'warning');
            
            return passedTests === totalTests;
        }
        
        // 字符串匹配测试
        function runStringMatchTest() {
            log('=== 开始字符串匹配测试 ===', 'info');
            setStatus('正在运行字符串匹配测试...', 'info');
            
            const testCases = [
                { str1: '苹果', str2: '苹果', expected: 1, description: '完全匹配' },
                { str1: '苹果', str2: '苹果 红富士', expected: 0.5, description: '部分匹配', threshold: 0.4 },
                { str1: '西红柿', str2: '番茄', expected: 0, description: '不同名称', threshold: 0.3 },
                { str1: '土豆', str2: '马铃薯', expected: 0, description: '别名', threshold: 0.3 },
                { str1: '', str2: '苹果', expected: 0, description: '空字符串' },
                { str1: '苹果', str2: '', expected: 0, description: '空字符串2' }
            ];
            
            let passedTests = 0;
            let totalTests = testCases.length;
            
            testCases.forEach((testCase, index) => {
                log(`\n--- 测试 ${index + 1}: ${testCase.description} ---`);
                const result = calculateSimilarity(testCase.str1, testCase.str2);
                const threshold = testCase.threshold || 0.8;
                const passed = testCase.expected === 1 ? result >= threshold : 
                              testCase.expected === 0 ? result < threshold : 
                              Math.abs(result - testCase.expected) < 0.2;
                
                log(`字符串1: "${testCase.str1}"`);
                log(`字符串2: "${testCase.str2}"`);
                log(`相似度: ${result.toFixed(3)}`);
                log(`阈值: ${threshold}`);
                log(`结果: ${passed ? '通过' : '失败'}`, passed ? 'success' : 'error');
                
                if (passed) passedTests++;
            });
            
            log(`\n=== 字符串匹配测试完成 ===`, 'info');
            log(`通过: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`, passedTests === totalTests ? 'success' : 'warning');
            
            setStatus(`字符串匹配测试完成: ${passedTests}/${totalTests} 通过`, passedTests === totalTests ? 'success' : 'warning');
            
            return passedTests === totalTests;
        }
        
        // 运行所有测试
        function runAllTests() {
            clearConsole();
            log('🚀 开始运行所有测试...', 'info');
            setStatus('正在运行所有测试...', 'info');
            
            const results = [];
            
            // 运行价格解析测试
            results.push(runPriceParserTest());
            
            log('\n' + '='.repeat(50), 'info');
            
            // 运行字符串匹配测试
            results.push(runStringMatchTest());
            
            log('\n' + '='.repeat(50), 'info');
            
            // 总结
            const passedTests = results.filter(r => r).length;
            const totalTests = results.length;
            
            log(`\n🎯 所有测试完成!`, 'info');
            log(`总体结果: ${passedTests}/${totalTests} 个测试套件通过`, passedTests === totalTests ? 'success' : 'warning');
            
            if (passedTests === totalTests) {
                log(`🎉 恭喜! 所有核心功能测试通过!`, 'success');
                setStatus('🎉 所有测试通过! 核心功能正常', 'success');
            } else {
                log(`⚠️ 部分测试未通过，请检查相关功能`, 'warning');
                setStatus('⚠️ 部分测试未通过', 'warning');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('📱 独立测试页面加载完成', 'success');
            log('👆 点击上方按钮开始测试核心功能', 'info');
            setStatus('测试页面已准备就绪，点击按钮开始测试', 'success');
        });
    </script>
</body>
</html>
