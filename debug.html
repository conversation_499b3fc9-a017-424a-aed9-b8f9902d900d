<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-box { border: 2px dashed #ccc; padding: 20px; margin: 10px 0; text-align: center; }
        .file-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>🛒 菜篮子智能比价工具 - 调试版本</h1>
    
    <div class="upload-box">
        <h3>销售价目表</h3>
        <input type="file" id="salesFile" accept=".xlsx,.xls">
        <div id="salesInfo" class="file-info hidden"></div>
    </div>
    
    <div class="upload-box">
        <h3>菜篮子价格表</h3>
        <input type="file" id="basketFile" accept=".xlsx,.xls">
        <div id="basketInfo" class="file-info hidden"></div>
    </div>
    
    <button id="compareBtn" disabled>开始比价分析</button>
    <button id="clearBtn">清空数据</button>
    
    <div id="status"></div>
    <div id="results" class="hidden"></div>
    
    <script>
        console.log('Debug version loaded');
        console.log('XLSX available:', typeof XLSX !== 'undefined');
        
        let salesData = null;
        let basketData = null;
        
        // 文件上传处理
        document.getElementById('salesFile').addEventListener('change', async function(e) {
            console.log('Sales file selected');
            await handleFileUpload(e, 'sales');
        });
        
        document.getElementById('basketFile').addEventListener('change', async function(e) {
            console.log('Basket file selected');
            await handleFileUpload(e, 'basket');
        });
        
        // 比价按钮
        document.getElementById('compareBtn').addEventListener('click', function() {
            console.log('Compare button clicked');
            performComparison();
        });
        
        // 清空按钮
        document.getElementById('clearBtn').addEventListener('click', function() {
            console.log('Clear button clicked');
            clearData();
        });
        
        async function handleFileUpload(event, type) {
            const file = event.target.files[0];
            if (!file) return;
            
            console.log(`Processing ${type} file:`, file.name, file.size);
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div>正在处理 ${type === 'sales' ? '销售价目表' : '菜篮子价格表'}...</div>`;
            
            try {
                const data = await parseExcelFile(file);
                console.log(`${type} data parsed:`, data.length, 'rows');
                
                if (type === 'sales') {
                    salesData = data;
                    updateFileInfo('salesInfo', file.name, data);
                } else {
                    basketData = data;
                    updateFileInfo('basketInfo', file.name, data);
                }
                
                checkReadyToCompare();
                statusDiv.innerHTML = `<div class="success">${type === 'sales' ? '销售价目表' : '菜篮子价格表'}上传成功！</div>`;
                
            } catch (error) {
                console.error(`Error processing ${type} file:`, error);
                statusDiv.innerHTML = `<div class="error">文件处理失败: ${error.message}</div>`;
            }
        }
        
        async function parseExcelFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        
                        console.log('Raw Excel data preview:', jsonData.slice(0, 5));
                        
                        const processedData = processExcelData(jsonData);
                        resolve(processedData);
                    } catch (error) {
                        reject(new Error('Excel文件解析失败: ' + error.message));
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(file);
            });
        }
        
        function processExcelData(rawData) {
            if (!rawData || rawData.length < 2) {
                throw new Error('Excel文件数据不足');
            }
            
            // 查找表头行
            let headerRowIndex = 0;
            const keyFields = ['物料名称', '商品', '名称', '计价单位', '单位', '价格', '市场价', '本期'];
            
            for (let i = 0; i < Math.min(rawData.length, 10); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const hasKeyFields = row.some(cell => 
                        cell && typeof cell === 'string' && 
                        keyFields.some(field => cell.includes(field))
                    );
                    if (hasKeyFields) {
                        headerRowIndex = i;
                        console.log('Found header at row:', i, row);
                        break;
                    }
                }
            }
            
            const headers = rawData[headerRowIndex];
            const dataRows = rawData.slice(headerRowIndex + 1);
            
            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    headers.forEach((header, index) => {
                        if (header) {
                            obj[header] = row[index] || '';
                        }
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && String(val).trim().length > 0);
                    const isCompanyInfo = values.some(val => 
                        val && String(val).includes('广东金勺子食品有限公司')
                    );
                    return hasContent && !isCompanyInfo;
                });
                
            console.log('Processed data:', processedData.length, 'rows');
            return processedData;
        }
        
        function updateFileInfo(infoId, fileName, data) {
            const infoDiv = document.getElementById(infoId);
            const columns = data.length > 0 ? Object.keys(data[0]) : [];
            
            infoDiv.innerHTML = `
                <strong>文件名:</strong> ${fileName}<br>
                <strong>数据行数:</strong> ${data.length}<br>
                <strong>列数:</strong> ${columns.length}<br>
                <strong>列名:</strong> ${columns.slice(0, 5).join(', ')}${columns.length > 5 ? '...' : ''}
            `;
            infoDiv.classList.remove('hidden');
        }
        
        function checkReadyToCompare() {
            const compareBtn = document.getElementById('compareBtn');
            compareBtn.disabled = !(salesData && basketData);
            
            if (salesData && basketData) {
                console.log('Both files ready for comparison');
            }
        }
        
        function performComparison() {
            console.log('Starting comparison...');
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = `
                <h3>比价结果</h3>
                <p>销售价目表: ${salesData.length} 行</p>
                <p>菜篮子价格表: ${basketData.length} 行</p>
                <p>功能开发中...</p>
            `;
            resultsDiv.classList.remove('hidden');
        }
        
        function clearData() {
            salesData = null;
            basketData = null;
            document.getElementById('salesFile').value = '';
            document.getElementById('basketFile').value = '';
            document.getElementById('salesInfo').classList.add('hidden');
            document.getElementById('basketInfo').classList.add('hidden');
            document.getElementById('results').classList.add('hidden');
            document.getElementById('status').innerHTML = '';
            checkReadyToCompare();
        }
    </script>
</body>
</html>
