<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-log { background: #000; color: #0f0; padding: 15px; border-radius: 4px; 
                     font-family: monospace; font-size: 12px; max-height: 500px; 
                     overflow-y: auto; margin: 20px 0; white-space: pre-wrap; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; 
               border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>🔍 简化调试页面</h1>
    
    <div class="section">
        <h3>测试步骤</h3>
        <button class="btn" onclick="testDataLoading()">1️⃣ 测试数据加载</button>
        <button class="btn" onclick="testDataStructure()">2️⃣ 测试数据结构分析</button>
        <button class="btn" onclick="testPriceMatching()">3️⃣ 测试价格匹配</button>
        <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
    </div>

    <div class="debug-log" id="debugLog">点击按钮开始测试...</div>

    <script>
        let salesData = null;
        let basketData = null;
        const debugLog = document.getElementById('debugLog');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            debugLog.textContent = '';
        }

        async function testDataLoading() {
            log('🚀 开始测试数据加载...');
            
            try {
                // 测试加载销售数据
                log('📁 加载销售价目表...');
                const salesResponse = await fetch('2025年7月销售价目表2025.xlsx');
                if (salesResponse.ok) {
                    const salesBlob = await salesResponse.blob();
                    salesData = await readExcelFile(salesBlob);
                    log(`✅ 销售数据加载成功: ${salesData.length} 行`);
                    log(`销售数据样本: ${JSON.stringify(salesData[0], null, 2)}`);
                } else {
                    log(`❌ 销售数据加载失败: ${salesResponse.status}`);
                }

                // 测试加载菜篮子数据
                log('📁 加载菜篮子价格表...');
                const basketResponse = await fetch('广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx');
                if (basketResponse.ok) {
                    const basketBlob = await basketResponse.blob();
                    basketData = await readExcelFile(basketBlob);
                    log(`✅ 菜篮子数据加载成功: ${basketData.length} 行`);
                    log(`菜篮子数据样本: ${JSON.stringify(basketData[0], null, 2)}`);
                } else {
                    log(`❌ 菜篮子数据加载失败: ${basketResponse.status}`);
                }

            } catch (error) {
                log(`❌ 数据加载错误: ${error.message}`);
            }
        }

        async function testDataStructure() {
            if (!salesData || !basketData) {
                log('❌ 请先加载数据');
                return;
            }

            log('🔍 分析数据结构...');
            
            // 分析销售数据
            const salesColumns = Object.keys(salesData[0]);
            log(`销售数据列名: ${salesColumns.join(', ')}`);
            
            // 分析菜篮子数据
            const basketColumns = Object.keys(basketData[0]);
            log(`菜篮子数据列名: ${basketColumns.join(', ')}`);
            
            // 查找日期列
            log('🗓️ 查找日期相关列...');
            const dateColumns = [];
            basketColumns.forEach((col, index) => {
                if (col && (col.includes('日') || col.includes('价格') || col.includes('单价') || col.includes('元'))) {
                    dateColumns.push({ column: col, index: index });
                    log(`  找到可能的价格列: [${index}] ${col}`);
                }
            });
            
            // 检查价格列的数据
            if (dateColumns.length > 0) {
                const priceColumn = dateColumns[0].column;
                log(`🔍 检查价格列 "${priceColumn}" 的数据:`);
                for (let i = 0; i < Math.min(5, basketData.length); i++) {
                    const value = basketData[i][priceColumn];
                    log(`  行${i+1}: "${value}" (类型: ${typeof value})`);
                }
            }
        }

        async function testPriceMatching() {
            if (!salesData || !basketData) {
                log('❌ 请先加载数据');
                return;
            }

            log('🔄 测试价格匹配...');
            
            // 找到列名
            const salesColumns = Object.keys(salesData[0]);
            const basketColumns = Object.keys(basketData[0]);
            
            // 简单的列名匹配
            const productNameCol = salesColumns.find(col => col.includes('商品') || col.includes('名称')) || salesColumns[0];
            const unitCol = salesColumns.find(col => col.includes('单位')) || salesColumns[1];
            const priceCol = salesColumns.find(col => col.includes('价格') || col.includes('单价')) || salesColumns[2];
            
            const basketProductCol = basketColumns.find(col => col.includes('商品') || col.includes('名称')) || basketColumns[0];
            const basketUnitCol = basketColumns.find(col => col.includes('单位'));
            const basketPriceCol = basketColumns.find(col => col.includes('日') || col.includes('价格')) || basketColumns[basketColumns.length - 1];
            
            log(`使用列: 商品=${productNameCol}, 单位=${unitCol}, 价格=${priceCol}`);
            log(`菜篮子列: 商品=${basketProductCol}, 单位=${basketUnitCol}, 价格=${basketPriceCol}`);
            
            // 测试前3个商品的匹配
            for (let i = 0; i < Math.min(3, salesData.length); i++) {
                const salesItem = salesData[i];
                const productName = salesItem[productNameCol];
                const unit = salesItem[unitCol];
                const salesPrice = parsePrice(salesItem[priceCol]);
                
                log(`\n--- 测试商品 ${i+1}: ${productName} (${unit}) ---`);
                log(`销售价格: ${salesPrice}`);
                
                // 查找匹配
                let basketMatch = null;
                let basketPrice = null;
                
                for (const basketItem of basketData) {
                    const basketProduct = basketItem[basketProductCol];
                    const basketUnit = basketItem[basketUnitCol];
                    
                    if (basketProduct === productName && basketUnit === unit) {
                        const priceValue = basketItem[basketPriceCol];
                        basketPrice = parsePrice(priceValue);
                        log(`✅ 精确匹配: ${basketProduct} - 原始价格值: "${priceValue}" - 解析后: ${basketPrice}`);
                        basketMatch = basketItem;
                        break;
                    }
                }
                
                if (!basketMatch) {
                    // 尝试包含匹配
                    for (const basketItem of basketData) {
                        const basketProduct = basketItem[basketProductCol];
                        if (basketProduct && (basketProduct.includes(productName) || productName.includes(basketProduct))) {
                            const priceValue = basketItem[basketPriceCol];
                            basketPrice = parsePrice(priceValue);
                            log(`🔍 包含匹配: ${basketProduct} - 原始价格值: "${priceValue}" - 解析后: ${basketPrice}`);
                            basketMatch = basketItem;
                            break;
                        }
                    }
                }
                
                if (!basketMatch) {
                    log(`❌ 未找到匹配`);
                } else {
                    // 计算差异
                    if (basketPrice !== null && salesPrice !== null) {
                        const diff = basketPrice - salesPrice;
                        const diffPercent = salesPrice !== 0 ? (diff / salesPrice * 100) : 0;
                        log(`💰 价格差异: ${diff.toFixed(2)} (${diffPercent.toFixed(1)}%)`);
                    }
                }
            }
        }

        function parsePrice(priceStr) {
            if (priceStr === null || priceStr === undefined || priceStr === '') {
                return null;
            }
            
            if (typeof priceStr === 'number') {
                return isNaN(priceStr) ? null : priceStr;
            }
            
            let priceString = String(priceStr).trim();
            
            // 处理特殊情况
            if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
                return null;
            }
            
            // 移除非数字字符
            const cleanPrice = priceString.replace(/[^\d.-]/g, '');
            
            if (!cleanPrice) {
                return null;
            }
            
            const price = parseFloat(cleanPrice);
            return isNaN(price) ? null : price;
        }

        async function readExcelFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        
                        // 转换为对象格式
                        const headers = jsonData[0];
                        const rows = jsonData.slice(1).map(row => {
                            const obj = {};
                            headers.forEach((header, index) => {
                                obj[header] = row[index];
                            });
                            return obj;
                        });
                        
                        resolve(rows);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(file);
            });
        }
    </script>
</body>
</html>
