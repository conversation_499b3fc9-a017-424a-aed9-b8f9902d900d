<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 500px; overflow-y: auto; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>快速测试 - 数据解析</h1>
    <button onclick="testDataParsing()">测试数据解析</button>
    <button onclick="clearConsole()">清空控制台</button>
    
    <div id="console" class="console"></div>
    
    <script>
        function log(message) {
            const console = document.getElementById('console');
            console.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
        }
        
        async function testDataParsing() {
            log('开始测试数据解析...');
            
            try {
                // 测试销售价目表
                log('正在加载销售价目表...');
                const salesResponse = await fetch('2025年7月销售价目表2025.xlsx');
                if (!salesResponse.ok) {
                    throw new Error('无法加载销售价目表');
                }
                const salesBlob = await salesResponse.blob();
                const salesData = await parseExcelFile(salesBlob);
                log('销售价目表解析完成，行数: ' + salesData.length);
                log('销售价目表前3行: ' + JSON.stringify(salesData.slice(0, 3), null, 2));
                
                // 测试菜篮子价格表
                log('正在加载菜篮子价格表...');
                const basketResponse = await fetch('广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx');
                if (!basketResponse.ok) {
                    throw new Error('无法加载菜篮子价格表');
                }
                const basketBlob = await basketResponse.blob();
                const basketData = await parseExcelFile(basketBlob);
                log('菜篮子价格表解析完成，行数: ' + basketData.length);
                log('菜篮子价格表前3行: ' + JSON.stringify(basketData.slice(0, 3), null, 2));
                
                // 分析列结构
                if (basketData.length > 0) {
                    const columns = Object.keys(basketData[0]);
                    log('菜篮子价格表列名: ' + JSON.stringify(columns));
                    
                    // 查找日期相关的列
                    const dateColumns = columns.filter(col => 
                        col && (col.includes('日') || col.includes('月') || col.includes('价格') || /\d+/.test(col))
                    );
                    log('可能的日期/价格列: ' + JSON.stringify(dateColumns));
                    
                    // 检查每个可能的价格列的数据
                    dateColumns.forEach(col => {
                        log(`列 "${col}" 的前5个值:`);
                        for (let i = 0; i < Math.min(5, basketData.length); i++) {
                            const value = basketData[i][col];
                            log(`  行${i+1}: "${value}" (类型: ${typeof value})`);
                        }
                    });
                }
                
            } catch (error) {
                log('错误: ' + error.message);
                console.error(error);
            }
        }
        
        async function parseExcelFile(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        
                        log('原始数据行数: ' + rawData.length);
                        log('原始数据前3行: ' + JSON.stringify(rawData.slice(0, 3), null, 2));
                        
                        const processedData = processExcelData(rawData);
                        resolve(processedData);
                    } catch (error) {
                        reject(new Error('Excel文件解析失败: ' + error.message));
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(blob);
            });
        }
        
        function processExcelData(rawData) {
            if (!rawData || rawData.length < 2) {
                throw new Error('Excel文件数据不足');
            }
            
            log('开始处理Excel数据...');
            
            // 检查是否是菜篮子价格表
            const isBasketPriceTable = rawData.some(row =>
                row && row.some(cell =>
                    cell && typeof cell === 'string' &&
                    (cell.includes('菜篮子价格监测') ||
                     cell.includes('监测日报表') ||
                     cell.includes('监测对比表') ||
                     (cell.includes('商品/分类名称') && rawData.some(r =>
                        r && r.some(c => c && c.toString().includes('监测'))
                     )))
                )
            );
            
            log('是否为菜篮子价格表: ' + isBasketPriceTable);
            
            if (isBasketPriceTable) {
                return parseBasketPriceTable(rawData);
            } else {
                return parseRegularTable(rawData);
            }
        }
        
        function parseBasketPriceTable(rawData) {
            log('解析菜篮子价格表...');
            
            // 查找主表头行
            let headerRowIndex = -1;
            const keyFields = ['商品', '名称', '计价单位', '单位', '规格', '等级', '产地'];
            
            for (let i = 0; i < Math.min(rawData.length, 10); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const hasKeyFields = row.some(cell => 
                        cell && typeof cell === 'string' && 
                        keyFields.some(field => cell.includes(field))
                    );
                    if (hasKeyFields) {
                        headerRowIndex = i;
                        log('找到表头行在第 ' + (i + 1) + ' 行: ' + JSON.stringify(row));
                        break;
                    }
                }
            }
            
            if (headerRowIndex === -1) {
                headerRowIndex = 1;
                log('使用默认表头行索引: 1');
            }
            
            const maxColumns = Math.max(...rawData.map(row => row ? row.length : 0));
            log('最大列数: ' + maxColumns);
            
            const headers = [];
            for (let col = 0; col < maxColumns; col++) {
                let header = '';
                for (let row = 0; row <= headerRowIndex; row++) {
                    if (rawData[row] && rawData[row][col]) {
                        const cellValue = String(rawData[row][col]).trim();
                        if (cellValue && !header.includes(cellValue)) {
                            header += (header ? '_' : '') + cellValue;
                        }
                    }
                }
                headers[col] = header || `列${col + 1}`;
            }
            
            log('合并后的表头: ' + JSON.stringify(headers));
            
            const dataRows = rawData.slice(headerRowIndex + 1);
            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    headers.forEach((header, index) => {
                        obj[header] = row[index] || '';
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && val.toString().trim().length > 0);
                    return hasContent;
                });
            
            log('菜篮子价格表处理完成，数据行数: ' + processedData.length);
            return processedData;
        }
        
        function parseRegularTable(rawData) {
            log('解析常规表格...');
            
            let headerRowIndex = 0;
            const keyFields = ['物料名称', '商品', '名称', '计价单位', '单位', '价格', '市场价', '本期'];
            
            for (let i = 0; i < Math.min(rawData.length, 10); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const hasKeyFields = row.some(cell => 
                        cell && typeof cell === 'string' && 
                        keyFields.some(field => cell.includes(field))
                    );
                    if (hasKeyFields) {
                        headerRowIndex = i;
                        log('找到表头行在第 ' + (i + 1) + ' 行: ' + JSON.stringify(row));
                        break;
                    }
                }
            }
            
            const headers = rawData[headerRowIndex];
            const dataRows = rawData.slice(headerRowIndex + 1);
            
            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    headers.forEach((header, index) => {
                        if (header) {
                            obj[header] = row[index] || '';
                        }
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && String(val).trim().length > 0);
                    const isCompanyInfo = values.some(val => 
                        val && String(val).includes('广东金勺子食品有限公司')
                    );
                    return hasContent && !isCompanyInfo;
                });
                
            log('常规表格处理完成，数据行数: ' + processedData.length);
            return processedData;
        }
    </script>
</body>
</html>
