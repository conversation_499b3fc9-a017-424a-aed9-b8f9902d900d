<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟数据调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-log { background: #000; color: #0f0; padding: 15px; border-radius: 4px; 
                     font-family: monospace; font-size: 12px; max-height: 500px; 
                     overflow-y: auto; margin: 20px 0; white-space: pre-wrap; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; 
               border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .results { margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .matched { color: green; }
        .not-found { color: red; }
    </style>
</head>
<body>
    <h1>🔍 模拟数据调试页面</h1>
    
    <div class="section">
        <h3>测试步骤</h3>
        <button class="btn" onclick="testWithMockData()">🧪 使用模拟数据测试</button>
        <button class="btn" onclick="testPriceParsingOnly()">💰 测试价格解析</button>
        <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
    </div>

    <div class="debug-log" id="debugLog">点击按钮开始测试...</div>

    <div class="results" id="results"></div>

    <script>
        const debugLog = document.getElementById('debugLog');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            debugLog.textContent = '';
        }

        // 模拟销售数据
        const mockSalesData = [
            { '商品名称': '白萝卜', '单位': '公斤', '单价': 3.5 },
            { '商品名称': '胡萝卜', '单位': '公斤', '单价': 4.2 },
            { '商品名称': '土豆', '单位': '公斤', '单价': 2.8 },
            { '商品名称': '西红柿', '单位': '公斤', '单价': 6.5 },
            { '商品名称': '黄瓜', '单位': '公斤', '单价': 5.0 }
        ];

        // 模拟菜篮子数据（基于真实数据格式）
        const mockBasketData = [
            { '商品/分类名称': '白萝卜', '计价单位': '公斤', '6月25日': '3.2', '6月26日': '3.3' },
            { '商品/分类名称': '胡萝卜', '计价单位': '公斤', '6月25日': '4.0', '6月26日': '4.1' },
            { '商品/分类名称': '马铃薯', '计价单位': '公斤', '6月25日': '2.5', '6月26日': '2.6' },
            { '商品/分类名称': '西红柿', '计价单位': '公斤', '6月25日': '6.8', '6月26日': '6.9' },
            { '商品/分类名称': '黄瓜', '计价单位': '公斤', '6月25日': '4.8', '6月26日': '4.9' },
            { '商品/分类名称': '蔬菜类平均', '计价单位': '公斤', '6月25日': '4.26', '6月26日': '4.36' }
        ];

        function testWithMockData() {
            log('🧪 开始使用模拟数据测试...');
            clearLog();
            log('🧪 开始使用模拟数据测试...');
            
            // 1. 分析数据结构
            log('\n📊 分析销售数据结构:');
            const salesColumns = Object.keys(mockSalesData[0]);
            log(`销售数据列名: ${salesColumns.join(', ')}`);
            log(`销售数据样本: ${JSON.stringify(mockSalesData[0])}`);
            
            log('\n📊 分析菜篮子数据结构:');
            const basketColumns = Object.keys(mockBasketData[0]);
            log(`菜篮子数据列名: ${basketColumns.join(', ')}`);
            log(`菜篮子数据样本: ${JSON.stringify(mockBasketData[0])}`);
            
            // 2. 查找价格列
            log('\n🔍 查找价格列:');
            const dateColumns = basketColumns.filter(col => col.includes('日') || col.includes('价格'));
            log(`找到的日期/价格列: ${dateColumns.join(', ')}`);
            
            const bestPriceColumn = dateColumns[0]; // 使用第一个找到的日期列
            log(`选择的价格列: ${bestPriceColumn}`);
            
            // 3. 检查价格列数据
            log(`\n💰 检查价格列 "${bestPriceColumn}" 的数据:`);
            mockBasketData.forEach((item, index) => {
                const value = item[bestPriceColumn];
                log(`  行${index+1}: "${value}" (类型: ${typeof value})`);
            });
            
            // 4. 测试商品匹配和价格计算
            log('\n🔄 测试商品匹配:');
            const results = [];
            
            mockSalesData.forEach((salesItem, index) => {
                const productName = salesItem['商品名称'];
                const unit = salesItem['单位'];
                const salesPrice = salesItem['单价'];
                
                log(`\n--- 商品 ${index+1}: ${productName} (${unit}) ---`);
                log(`销售价格: ${salesPrice}`);
                
                // 查找匹配
                let basketMatch = null;
                let basketPrice = null;
                
                // 精确匹配
                basketMatch = mockBasketData.find(item => 
                    item['商品/分类名称'] === productName && item['计价单位'] === unit
                );
                
                if (basketMatch) {
                    const priceValue = basketMatch[bestPriceColumn];
                    basketPrice = parsePrice(priceValue);
                    log(`✅ 精确匹配: ${basketMatch['商品/分类名称']} - 原始价格: "${priceValue}" - 解析后: ${basketPrice}`);
                } else {
                    // 包含匹配或别名匹配
                    if (productName === '土豆') {
                        basketMatch = mockBasketData.find(item => item['商品/分类名称'] === '马铃薯');
                        if (basketMatch) {
                            const priceValue = basketMatch[bestPriceColumn];
                            basketPrice = parsePrice(priceValue);
                            log(`🔍 别名匹配: 土豆 -> 马铃薯 - 原始价格: "${priceValue}" - 解析后: ${basketPrice}`);
                        }
                    }
                }
                
                if (!basketMatch) {
                    log(`❌ 未找到匹配`);
                }
                
                // 计算价格差异
                let priceDiff = null;
                let priceDiffPercent = null;
                
                if (basketPrice !== null && salesPrice !== null) {
                    priceDiff = basketPrice - salesPrice;
                    priceDiffPercent = salesPrice !== 0 ? (priceDiff / salesPrice * 100) : 0;
                    log(`💰 价格差异: ${priceDiff.toFixed(2)} (${priceDiffPercent.toFixed(1)}%)`);
                }
                
                results.push({
                    productName: productName,
                    unit: unit,
                    salesPrice: salesPrice,
                    basketPrice: basketPrice,
                    priceDiff: priceDiff,
                    priceDiffPercent: priceDiffPercent,
                    status: basketMatch ? 'matched' : 'not_found'
                });
            });
            
            // 5. 显示结果表格
            displayResults(results);
            
            log('\n✅ 测试完成！');
        }

        function testPriceParsingOnly() {
            log('💰 测试价格解析功能...');
            clearLog();
            log('💰 测试价格解析功能...');
            
            const testPrices = [
                '3.2',
                '4.56',
                '12.34元',
                '¥5.67',
                '8.9元/公斤',
                '无',
                '缺',
                '--',
                null,
                undefined,
                '',
                45.67,
                '0',
                '0.0'
            ];
            
            testPrices.forEach(price => {
                const result = parsePrice(price);
                log(`输入: "${price}" (${typeof price}) -> 输出: ${result}`);
            });
        }

        function parsePrice(priceStr) {
            log(`parsePrice 输入: "${priceStr}" (类型: ${typeof priceStr})`);

            if (priceStr === null || priceStr === undefined || priceStr === '') {
                log(`parsePrice 返回 null: 输入为空`);
                return null;
            }

            // 如果已经是数字，直接返回
            if (typeof priceStr === 'number') {
                log(`parsePrice 输入已是数字: ${priceStr}`);
                return isNaN(priceStr) ? null : priceStr;
            }

            // 转换为字符串
            let priceString = String(priceStr).trim();
            log(`parsePrice 字符串化: "${priceString}"`);

            // 处理特殊情况：如果包含"无"、"缺"、"-"等表示无价格的字符
            if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
                log(`parsePrice 识别为无价格标识: "${priceString}"`);
                return null;
            }

            // 移除货币符号、单位等非数字字符，但保留数字、小数点和负号
            const cleanPrice = priceString.replace(/[^\d.-]/g, '');
            log(`parsePrice 清理后: "${cleanPrice}"`);

            // 如果清理后为空，返回null
            if (!cleanPrice) {
                log(`parsePrice 清理后为空`);
                return null;
            }

            const price = parseFloat(cleanPrice);
            log(`parsePrice 解析结果: ${price} (isNaN: ${isNaN(price)})`);

            return isNaN(price) ? null : price;
        }

        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<h3>📊 比价结果</h3><table>';
            html += '<tr><th>商品名称</th><th>单位</th><th>销售价格</th><th>菜篮子价格</th><th>价格差异</th><th>差异百分比</th><th>状态</th></tr>';
            
            results.forEach(result => {
                const statusClass = result.status === 'matched' ? 'matched' : 'not-found';
                html += `<tr>
                    <td>${result.productName}</td>
                    <td>${result.unit}</td>
                    <td>¥${result.salesPrice.toFixed(2)}</td>
                    <td>${result.basketPrice !== null ? '¥' + result.basketPrice.toFixed(2) : '-'}</td>
                    <td>${result.priceDiff !== null ? (result.priceDiff >= 0 ? '+' : '') + result.priceDiff.toFixed(2) : '-'}</td>
                    <td>${result.priceDiffPercent !== null ? (result.priceDiffPercent >= 0 ? '+' : '') + result.priceDiffPercent.toFixed(1) + '%' : '-'}</td>
                    <td class="${statusClass}">${result.status === 'matched' ? '✅ 已匹配' : '❌ 未找到'}</td>
                </tr>`;
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
