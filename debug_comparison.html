<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比价功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .upload-box {
            display: inline-block;
            margin: 10px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            min-width: 200px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .debug-log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .results-table th,
        .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #f2f2f2;
        }
        .status-matched { color: green; }
        .status-not-found { color: red; }
        .price-positive { color: red; }
        .price-negative { color: green; }
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 比价功能调试页面</h1>
        
        <div class="upload-section">
            <h3>📁 文件上传</h3>
            <div class="upload-box">
                <h4>销售价目表</h4>
                <input type="file" id="salesFile" accept=".xlsx,.xls">
                <div class="file-info" id="salesFileInfo"></div>
            </div>
            <div class="upload-box">
                <h4>菜篮子价格表</h4>
                <input type="file" id="basketFile" accept=".xlsx,.xls">
                <div class="file-info" id="basketFileInfo"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="loadDemoBtn">📋 加载示例文件</button>
            <button class="btn" id="analyzeBtn" disabled>🔍 开始分析</button>
            <button class="btn" id="clearLogBtn">🗑️ 清空日志</button>
        </div>

        <div class="debug-log" id="debugLog">等待开始分析...</div>

        <div id="resultsSection" style="display: none;">
            <h3>📊 比价结果</h3>
            <table class="results-table" id="resultsTable">
                <thead>
                    <tr>
                        <th>商品名称</th>
                        <th>单位</th>
                        <th>销售价格</th>
                        <th>菜篮子价格</th>
                        <th>价格差异</th>
                        <th>差异百分比</th>
                        <th>匹配状态</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        class DebugComparison {
            constructor() {
                this.salesData = null;
                this.basketData = null;
                this.comparisonResults = null;
                this.debugLog = document.getElementById('debugLog');
                this.bindEvents();
            }

            bindEvents() {
                document.getElementById('salesFile').addEventListener('change', (e) => this.handleSalesFile(e));
                document.getElementById('basketFile').addEventListener('change', (e) => this.handleBasketFile(e));
                document.getElementById('loadDemoBtn').addEventListener('click', () => this.loadDemoFiles());
                document.getElementById('analyzeBtn').addEventListener('click', () => this.startAnalysis());
                document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.debugLog.textContent += `[${timestamp}] ${message}\n`;
                this.debugLog.scrollTop = this.debugLog.scrollHeight;
                console.log(message);
            }

            clearLog() {
                this.debugLog.textContent = '';
            }

            async handleSalesFile(event) {
                const file = event.target.files[0];
                if (file) {
                    this.log(`📁 加载销售价目表: ${file.name}`);
                    try {
                        this.salesData = await this.readExcelFile(file);
                        this.log(`✅ 销售价目表加载成功，共 ${this.salesData.length} 行数据`);
                        document.getElementById('salesFileInfo').textContent = `已加载: ${file.name} (${this.salesData.length} 行)`;
                        this.checkReadyToAnalyze();
                    } catch (error) {
                        this.log(`❌ 销售价目表加载失败: ${error.message}`);
                    }
                }
            }

            async handleBasketFile(event) {
                const file = event.target.files[0];
                if (file) {
                    this.log(`📁 加载菜篮子价格表: ${file.name}`);
                    try {
                        this.basketData = await this.readExcelFile(file);
                        this.log(`✅ 菜篮子价格表加载成功，共 ${this.basketData.length} 行数据`);
                        document.getElementById('basketFileInfo').textContent = `已加载: ${file.name} (${this.basketData.length} 行)`;
                        this.checkReadyToAnalyze();
                    } catch (error) {
                        this.log(`❌ 菜篮子价格表加载失败: ${error.message}`);
                    }
                }
            }

            checkReadyToAnalyze() {
                const ready = this.salesData && this.basketData;
                document.getElementById('analyzeBtn').disabled = !ready;
                if (ready) {
                    this.log('🎯 两个文件都已加载，可以开始分析');
                }
            }

            async loadDemoFiles() {
                this.log('📋 开始加载示例文件...');
                try {
                    // 加载销售价目表
                    const salesResponse = await fetch('2025年7月销售价目表2025.xlsx');
                    if (salesResponse.ok) {
                        const salesBlob = await salesResponse.blob();
                        this.salesData = await this.readExcelFile(salesBlob);
                        this.log(`✅ 示例销售价目表加载成功，共 ${this.salesData.length} 行数据`);
                        document.getElementById('salesFileInfo').textContent = `已加载示例文件 (${this.salesData.length} 行)`;
                    }

                    // 加载菜篮子价格表
                    const basketResponse = await fetch('广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx');
                    if (basketResponse.ok) {
                        const basketBlob = await basketResponse.blob();
                        this.basketData = await this.readExcelFile(basketBlob);
                        this.log(`✅ 示例菜篮子价格表加载成功，共 ${this.basketData.length} 行数据`);
                        document.getElementById('basketFileInfo').textContent = `已加载示例文件 (${this.basketData.length} 行)`;
                    }

                    this.checkReadyToAnalyze();
                } catch (error) {
                    this.log(`❌ 示例文件加载失败: ${error.message}`);
                }
            }

            async readExcelFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                            
                            // 转换为对象格式
                            const headers = jsonData[0];
                            const rows = jsonData.slice(1).map(row => {
                                const obj = {};
                                headers.forEach((header, index) => {
                                    obj[header] = row[index];
                                });
                                return obj;
                            });
                            
                            resolve(rows);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('文件读取失败'));
                    reader.readAsArrayBuffer(file);
                });
            }

            async startAnalysis() {
                this.log('🚀 开始比价分析...');
                this.clearLog();
                this.log('🚀 开始比价分析...');

                try {
                    // 重写console.log来捕获调试信息
                    const originalLog = console.log;
                    console.log = (...args) => {
                        originalLog.apply(console, args);
                        this.log(args.join(' '));
                    };

                    // 直接调用比价逻辑，不依赖PriceComparer类
                    await this.performDirectComparison();

                    // 恢复console.log
                    console.log = originalLog;

                    this.log('✅ 分析完成，显示结果...');
                    this.displayResults();

                } catch (error) {
                    this.log(`❌ 分析失败: ${error.message}`);
                    console.error('分析错误:', error);
                }
            }

            async performDirectComparison() {
                this.log('📊 分析销售数据结构...');
                const salesStructure = this.analyzeSalesDataStructure();
                this.log(`销售数据结构: ${JSON.stringify(salesStructure)}`);

                this.log('📊 分析菜篮子数据结构...');
                const basketStructure = this.analyzeBasketDataStructure();
                this.log(`菜篮子数据结构: ${JSON.stringify(basketStructure)}`);

                this.log('🔍 查找最佳日期列...');
                const bestDateColumn = this.findBestDateColumn(basketStructure);
                this.log(`最佳日期列: ${bestDateColumn}`);

                this.log('🔄 开始商品匹配...');
                const matchedProducts = this.matchProducts(salesStructure, basketStructure, bestDateColumn);
                this.log(`匹配完成，共处理 ${matchedProducts.length} 个商品`);

                this.log('💰 计算价格差异...');
                this.comparisonResults = this.calculatePriceDifferences(matchedProducts);
                this.log(`价格差异计算完成`);
            }

            displayResults() {
                if (!this.comparisonResults) {
                    this.log('❌ 没有分析结果可显示');
                    return;
                }

                const resultsSection = document.getElementById('resultsSection');
                const tbody = document.getElementById('resultsTableBody');
                
                tbody.innerHTML = '';
                
                this.comparisonResults.forEach(result => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${result.productName}</td>
                        <td>${result.unit}</td>
                        <td>${result.salesPrice !== null ? '¥' + result.salesPrice.toFixed(2) : '-'}</td>
                        <td>${result.basketPrice !== null ? '¥' + result.basketPrice.toFixed(2) : '-'}</td>
                        <td class="${result.priceDiff > 0 ? 'price-positive' : 'price-negative'}">
                            ${result.priceDiff !== null ? (result.priceDiff >= 0 ? '+' : '') + result.priceDiff.toFixed(2) : '-'}
                        </td>
                        <td class="${result.priceDiffPercent > 0 ? 'price-positive' : 'price-negative'}">
                            ${result.priceDiffPercent !== null ? (result.priceDiffPercent >= 0 ? '+' : '') + result.priceDiffPercent.toFixed(1) + '%' : '-'}
                        </td>
                        <td class="status-${result.status}">
                            ${result.status === 'matched' ? '✅ 已匹配' : '❌ 未找到'}
                            ${result.matchType ? ` (${result.matchType})` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                resultsSection.style.display = 'block';
                this.log(`📊 结果显示完成，共 ${this.comparisonResults.length} 个商品`);
            }

            // 分析销售数据结构
            analyzeSalesDataStructure() {
                if (!this.salesData || this.salesData.length === 0) {
                    throw new Error('销售价目表数据为空');
                }

                const firstRow = this.salesData[0];
                const columns = Object.keys(firstRow);

                this.log(`销售数据列名: ${columns.join(', ')}`);

                const structure = {
                    productNameColumn: this.findColumn(columns, ['商品名称', '品名', '名称', '商品']),
                    unitColumn: this.findColumn(columns, ['单位', '计价单位', '规格']),
                    priceColumn: this.findColumn(columns, ['单价', '价格', '销售价格', '售价']),
                    allColumns: columns,
                    dataCount: this.salesData.length
                };

                this.log(`找到销售数据列: 商品=${structure.productNameColumn}, 单位=${structure.unitColumn}, 价格=${structure.priceColumn}`);
                return structure;
            }

            // 分析菜篮子数据结构
            analyzeBasketDataStructure() {
                if (!this.basketData || this.basketData.length === 0) {
                    throw new Error('菜篮子价格表数据为空');
                }

                const firstRow = this.basketData[0];
                const columns = Object.keys(firstRow);

                this.log(`菜篮子数据列名: ${columns.join(', ')}`);

                const structure = {
                    productNameColumn: this.findColumn(columns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || columns[0],
                    unitColumn: this.findColumn(columns, ['计价单位', '单位', '规格', '计量单位']),
                    allColumns: columns,
                    dataCount: this.basketData.length,
                    dateColumns: this.findDateColumns(columns)
                };

                this.log(`找到菜篮子数据列: 商品=${structure.productNameColumn}, 单位=${structure.unitColumn}`);
                this.log(`找到日期列: ${structure.dateColumns.map(d => d.column).join(', ')}`);
                return structure;
            }

            // 查找列名
            findColumn(columns, possibleNames) {
                for (const name of possibleNames) {
                    const found = columns.find(col => col && col.includes(name));
                    if (found) return found;
                }
                return null;
            }

            // 查找日期列
            findDateColumns(columns) {
                const dateColumns = [];
                const datePattern = /(\d{1,2})[日号]/;

                for (let i = 0; i < columns.length; i++) {
                    const col = columns[i];
                    if (!col) continue;

                    if (datePattern.test(col) || col.includes('价格') || col.includes('单价') || col.includes('元')) {
                        const match = col.match(datePattern);
                        const day = match ? parseInt(match[1]) : 25;
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            priority: Math.abs(day - 25)
                        });
                    }
                }

                dateColumns.sort((a, b) => a.priority - b.priority);
                return dateColumns;
            }

            // 查找最佳日期列
            findBestDateColumn(basketStructure) {
                if (basketStructure.dateColumns.length === 0) {
                    throw new Error('未找到可用的日期价格列');
                }

                const bestDate = basketStructure.dateColumns[0];
                this.log(`选择最佳日期列: ${bestDate.column}`);

                // 检查数据样本
                if (this.basketData.length > 0) {
                    for (let i = 0; i < Math.min(3, this.basketData.length); i++) {
                        const value = this.basketData[i][bestDate.column];
                        this.log(`  行${i+1}: "${value}" (类型: ${typeof value})`);
                    }
                }

                return bestDate.column;
            }

            // 商品匹配
            matchProducts(salesStructure, basketStructure, bestDateColumn) {
                const matchedProducts = [];

                this.log(`开始匹配商品，使用价格列: ${bestDateColumn}`);

                for (const salesItem of this.salesData) {
                    const productName = salesItem[salesStructure.productNameColumn];
                    const unit = salesItem[salesStructure.unitColumn];
                    const salesPrice = this.parsePrice(salesItem[salesStructure.priceColumn]);

                    if (!productName || !unit || salesPrice === null) {
                        continue;
                    }

                    this.log(`处理商品: ${productName} (${unit}) - 销售价格: ${salesPrice}`);

                    // 查找匹配
                    const basketMatch = this.findBasketMatch(productName, unit, basketStructure, bestDateColumn);

                    matchedProducts.push({
                        productName: productName,
                        unit: unit,
                        salesPrice: salesPrice,
                        basketPrice: basketMatch ? basketMatch.price : null,
                        basketMatch: basketMatch,
                        matchStatus: basketMatch ? 'matched' : 'not_found',
                        matchType: basketMatch ? basketMatch.matchType : null
                    });

                    if (basketMatch) {
                        this.log(`  ✅ 找到匹配: ${basketMatch.item[basketStructure.productNameColumn]} - 价格: ${basketMatch.price}`);
                    } else {
                        this.log(`  ❌ 未找到匹配`);
                    }
                }

                return matchedProducts;
            }

            // 查找菜篮子匹配
            findBasketMatch(productName, unit, basketStructure, priceColumn) {
                // 精确匹配
                const exactMatch = this.basketData.find(item => {
                    const basketProductName = item[basketStructure.productNameColumn];
                    const basketUnit = item[basketStructure.unitColumn];
                    return basketProductName === productName && basketUnit === unit;
                });

                if (exactMatch) {
                    const priceValue = exactMatch[priceColumn];
                    const price = this.parsePrice(priceValue);
                    if (price !== null) {
                        return { item: exactMatch, price: price, matchType: 'exact' };
                    }
                }

                // 包含匹配
                const containsMatch = this.basketData.find(item => {
                    const basketProductName = item[basketStructure.productNameColumn];
                    const basketUnit = item[basketStructure.unitColumn];

                    if (!basketProductName || !basketUnit) return false;

                    const nameContains = basketProductName.includes(productName) || productName.includes(basketProductName);
                    const unitMatch = basketUnit === unit;

                    return nameContains && unitMatch;
                });

                if (containsMatch) {
                    const priceValue = containsMatch[priceColumn];
                    const price = this.parsePrice(priceValue);
                    if (price !== null) {
                        return { item: containsMatch, price: price, matchType: 'contains' };
                    }
                }

                return null;
            }

            // 价格解析
            parsePrice(priceStr) {
                if (priceStr === null || priceStr === undefined || priceStr === '') {
                    return null;
                }

                if (typeof priceStr === 'number') {
                    return isNaN(priceStr) ? null : priceStr;
                }

                let priceString = String(priceStr).trim();

                // 处理特殊情况
                if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
                    return null;
                }

                // 移除非数字字符
                const cleanPrice = priceString.replace(/[^\d.-]/g, '');

                if (!cleanPrice) {
                    return null;
                }

                const price = parseFloat(cleanPrice);
                return isNaN(price) ? null : price;
            }

            // 计算价格差异
            calculatePriceDifferences(matchedProducts) {
                const results = [];

                for (const product of matchedProducts) {
                    let priceDiff = null;
                    let priceDiffPercent = null;
                    let status = 'not_found';

                    if (product.basketPrice !== null && product.salesPrice !== null) {
                        priceDiff = product.basketPrice - product.salesPrice;
                        priceDiffPercent = product.salesPrice !== 0 ? (priceDiff / product.salesPrice * 100) : 0;
                        status = 'matched';
                    }

                    results.push({
                        productName: product.productName,
                        unit: product.unit,
                        salesPrice: product.salesPrice,
                        basketPrice: product.basketPrice,
                        priceDiff: priceDiff,
                        priceDiffPercent: priceDiffPercent,
                        status: status,
                        matchType: product.matchType
                    });
                }

                return results;
            }
        }

        // 等待页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new DebugComparison();
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
