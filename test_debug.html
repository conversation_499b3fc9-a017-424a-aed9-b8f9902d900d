<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-box { border: 2px dashed #ccc; padding: 20px; margin: 10px 0; text-align: center; }
        .file-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .hidden { display: none; }
        .console-output { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            font-family: monospace; 
            height: 300px; 
            overflow-y: auto; 
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🛒 菜篮子智能比价工具 - 调试测试</h1>
    
    <button id="loadExampleBtn">加载示例文件</button>
    <button id="testBtn" disabled>开始测试比价</button>
    
    <div id="status"></div>
    
    <h3>控制台输出:</h3>
    <div id="console" class="console-output"></div>
    
    <script src="script.js"></script>
    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const consoleDiv = document.getElementById('console');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        // 创建比价工具实例
        const compareTool = new PriceCompareTool();
        
        document.getElementById('loadExampleBtn').addEventListener('click', async function() {
            console.log('开始加载示例文件...');
            try {
                await compareTool.loadDemoFiles();
                document.getElementById('testBtn').disabled = false;
                document.getElementById('status').innerHTML = '<div class="success">示例文件加载成功！</div>';
            } catch (error) {
                console.log('加载示例文件失败:', error);
                document.getElementById('status').innerHTML = '<div class="error">加载失败: ' + error.message + '</div>';
            }
        });
        
        document.getElementById('testBtn').addEventListener('click', async function() {
            console.log('开始测试比价...');
            try {
                await compareTool.performComparison();
                document.getElementById('status').innerHTML = '<div class="success">比价完成！请查看控制台输出。</div>';
            } catch (error) {
                console.log('比价失败:', error);
                document.getElementById('status').innerHTML = '<div class="error">比价失败: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
