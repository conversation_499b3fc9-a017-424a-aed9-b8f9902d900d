<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 400px; overflow-y: auto; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>测试修复结果</h1>
    <button onclick="testPriceParser()">测试价格解析函数</button>
    <button onclick="testFullComparison()">测试完整比价流程</button>
    <button onclick="clearConsole()">清空控制台</button>
    
    <div id="status"></div>
    <div id="console" class="console"></div>

    <!-- 添加必要的隐藏元素以避免script.js中的错误 -->
    <div style="display: none;">
        <input type="file" id="salesFile">
        <input type="file" id="basketFile">
        <div id="salesFileInfo"></div>
        <div id="basketFileInfo"></div>
        <div id="salesFileBox"></div>
        <div id="basketFileBox"></div>
        <button id="compareBtn"></button>
        <div id="resultsSection"></div>
        <div id="errorSection"></div>
        <div id="progressSection"></div>
        <div id="totalMatched"></div>
        <div id="avgPriceDiff"></div>
        <div id="matchedDate"></div>
        <div id="resultsTableBody"></div>
    </div>

    <script src="script.js"></script>
    <script>
        function log(message) {
            const console = document.getElementById('console');
            console.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.scrollTop = console.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = message;
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
        }
        
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            log(message);
        };
        
        function testPriceParser() {
            log('=== 测试价格解析函数 ===');

            const compareTool = new PriceComparisonTool();
            
            // 测试各种价格格式
            const testCases = [
                '12.50',
                12.50,
                '￥15.80',
                '20元',
                '25.00元/斤',
                '30.5',
                '无',
                '缺',
                '--',
                '',
                null,
                undefined,
                '0',
                '0.00',
                'N/A',
                '45.67元/公斤'
            ];
            
            testCases.forEach(testCase => {
                const result = compareTool.parsePrice(testCase);
                log(`输入: ${JSON.stringify(testCase)} => 输出: ${result}`);
            });
            
            log('=== 价格解析测试完成 ===');
        }
        
        async function testFullComparison() {
            log('=== 测试完整比价流程 ===');
            setStatus('正在测试完整比价流程...', 'info');
            
            try {
                const compareTool = new PriceComparisonTool();
                
                // 加载示例文件
                log('正在加载示例文件...');
                await compareTool.loadDemoFiles();
                log('示例文件加载完成');
                
                // 执行比价
                log('正在执行比价分析...');
                await compareTool.performComparison();
                log('比价分析完成');
                
                // 检查结果
                if (compareTool.comparisonResults && compareTool.comparisonResults.length > 0) {
                    log('比价结果样本:');
                    const sample = compareTool.comparisonResults.slice(0, 5);
                    sample.forEach((result, index) => {
                        log(`商品${index + 1}: ${result.productName}`);
                        log(`  销售价格: ${result.salesPrice}`);
                        log(`  菜篮子价格: ${result.basketPrice}`);
                        log(`  价格差异: ${result.priceDiff}`);
                        log(`  差异百分比: ${result.priceDiffPercent}%`);
                        log(`  匹配状态: ${result.status}`);
                        log('---');
                    });
                    
                    // 统计有效数据
                    const validResults = compareTool.comparisonResults.filter(r => 
                        r.basketPrice !== null && r.salesPrice !== null
                    );
                    log(`总商品数: ${compareTool.comparisonResults.length}`);
                    log(`有效匹配数: ${validResults.length}`);
                    
                    if (validResults.length > 0) {
                        setStatus('测试成功！价格差异计算正常工作。', 'success');
                    } else {
                        setStatus('警告：没有找到有效的价格匹配。', 'error');
                    }
                } else {
                    setStatus('错误：没有生成比价结果。', 'error');
                }
                
            } catch (error) {
                log('测试失败: ' + error.message);
                setStatus('测试失败: ' + error.message, 'error');
                console.error(error);
            }
        }
    </script>
</body>
</html>
