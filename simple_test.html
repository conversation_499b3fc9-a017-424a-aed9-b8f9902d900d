<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试 - 菜篮子比价工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 400px; overflow-y: auto; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>简单测试 - 核心功能验证</h1>
    <button onclick="testPriceParser()">测试价格解析</button>
    <button onclick="testDataLoading()">测试数据加载</button>
    <button onclick="clearConsole()">清空控制台</button>
    
    <div id="status"></div>
    <div id="console" class="console"></div>
    
    <script>
        function log(message) {
            const console = document.getElementById('console');
            console.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.scrollTop = console.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = message;
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
        }
        
        // 简化的价格解析函数（从script.js复制）
        function parsePrice(priceStr) {
            log(`parsePrice 输入: "${priceStr}" (类型: ${typeof priceStr})`);
            
            if (priceStr === null || priceStr === undefined || priceStr === '') {
                log(`parsePrice 返回 null: 输入为空`);
                return null;
            }

            // 如果已经是数字，直接返回
            if (typeof priceStr === 'number') {
                log(`parsePrice 输入已是数字: ${priceStr}`);
                return isNaN(priceStr) ? null : priceStr;
            }

            // 转换为字符串
            let priceString = String(priceStr).trim();
            log(`parsePrice 字符串化: "${priceString}"`);
            
            // 处理特殊情况：如果包含"无"、"缺"、"-"等表示无价格的字符
            if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
                log(`parsePrice 识别为无价格标识: "${priceString}"`);
                return null;
            }
            
            // 移除货币符号、单位等非数字字符，但保留数字、小数点和负号
            const cleanPrice = priceString.replace(/[^\d.-]/g, '');
            log(`parsePrice 清理后: "${cleanPrice}"`);
            
            // 如果清理后为空，返回null
            if (!cleanPrice) {
                log(`parsePrice 清理后为空`);
                return null;
            }
            
            const price = parseFloat(cleanPrice);
            log(`parsePrice 解析结果: ${price} (isNaN: ${isNaN(price)})`);

            return isNaN(price) ? null : price;
        }
        
        function testPriceParser() {
            log('=== 测试价格解析函数 ===');
            setStatus('正在测试价格解析...', 'info');
            
            // 测试各种价格格式
            const testCases = [
                '12.50',
                12.50,
                '￥15.80',
                '20元',
                '25.00元/斤',
                '30.5',
                '无',
                '缺',
                '--',
                '',
                null,
                undefined,
                '0',
                '0.00',
                'N/A',
                '45.67元/公斤'
            ];
            
            let successCount = 0;
            testCases.forEach(testCase => {
                const result = parsePrice(testCase);
                log(`输入: ${JSON.stringify(testCase)} => 输出: ${result}`);
                if (result !== null || testCase === null || testCase === undefined || testCase === '' || testCase === '无' || testCase === '缺' || testCase === '--' || testCase === 'N/A') {
                    successCount++;
                }
            });
            
            log(`=== 价格解析测试完成: ${successCount}/${testCases.length} 通过 ===`);
            setStatus(`价格解析测试完成: ${successCount}/${testCases.length} 通过`, successCount === testCases.length ? 'success' : 'error');
        }
        
        async function testDataLoading() {
            log('=== 测试数据加载 ===');
            setStatus('正在测试数据加载...', 'info');
            
            try {
                // 测试销售价目表
                log('正在加载销售价目表...');
                const salesResponse = await fetch('2025年7月销售价目表2025.xlsx');
                if (!salesResponse.ok) {
                    throw new Error('无法加载销售价目表');
                }
                const salesBlob = await salesResponse.blob();
                log('销售价目表加载成功，大小: ' + salesBlob.size + ' bytes');
                
                // 测试菜篮子价格表
                log('正在加载菜篮子价格表...');
                const basketResponse = await fetch('广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx');
                if (!basketResponse.ok) {
                    throw new Error('无法加载菜篮子价格表');
                }
                const basketBlob = await basketResponse.blob();
                log('菜篮子价格表加载成功，大小: ' + basketBlob.size + ' bytes');
                
                // 简单解析测试
                log('正在解析Excel文件...');
                const salesData = await parseExcelFile(salesBlob);
                const basketData = await parseExcelFile(basketBlob);
                
                log('销售价目表解析完成，行数: ' + salesData.length);
                log('菜篮子价格表解析完成，行数: ' + basketData.length);
                
                if (salesData.length > 0) {
                    log('销售价目表列名: ' + Object.keys(salesData[0]).join(', '));
                }
                if (basketData.length > 0) {
                    log('菜篮子价格表列名: ' + Object.keys(basketData[0]).join(', '));
                }
                
                setStatus('数据加载测试成功！', 'success');
                
            } catch (error) {
                log('数据加载测试失败: ' + error.message);
                setStatus('数据加载测试失败: ' + error.message, 'error');
            }
        }
        
        async function parseExcelFile(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        
                        // 简单处理：找到第一个非空行作为表头
                        let headerRowIndex = 0;
                        for (let i = 0; i < rawData.length; i++) {
                            if (rawData[i] && rawData[i].some(cell => cell && cell.toString().trim())) {
                                headerRowIndex = i;
                                break;
                            }
                        }
                        
                        const headers = rawData[headerRowIndex];
                        const dataRows = rawData.slice(headerRowIndex + 1);
                        
                        const processedData = dataRows
                            .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                            .map(row => {
                                const obj = {};
                                headers.forEach((header, index) => {
                                    if (header) {
                                        obj[header] = row[index] || '';
                                    }
                                });
                                return obj;
                            })
                            .filter(obj => {
                                const values = Object.values(obj);
                                return values.some(val => val && String(val).trim().length > 0);
                            });
                            
                        resolve(processedData);
                    } catch (error) {
                        reject(new Error('Excel文件解析失败: ' + error.message));
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(blob);
            });
        }
    </script>
</body>
</html>
