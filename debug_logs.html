<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            border-radius: 8px;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .log-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-warning { color: #ffc107; }
        .log-error { color: #dc3545; }
        .search-box {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .highlight {
            background-color: yellow;
            color: black;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 菜篮子工具日志调试</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>拖拽销售价目表Excel文件到这里，或点击选择文件</p>
            <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
            <button onclick="document.getElementById('fileInput').click()">选择文件</button>
        </div>

        <div class="upload-area" id="basketUploadArea">
            <p>拖拽菜篮子价格表Excel文件到这里，或点击选择文件</p>
            <input type="file" id="basketFileInput" accept=".xlsx,.xls" style="display: none;">
            <button onclick="document.getElementById('basketFileInput').click()">选择文件</button>
        </div>

        <!-- 比价分析按钮 -->
        <div style="text-align: center; margin: 20px 0;">
            <button id="compareBtn" onclick="startComparison()" class="upload-btn" style="background: #28a745; font-size: 18px; padding: 15px 30px;" disabled>
                🔍 开始比价分析
            </button>
        </div>

        <div>
            <button onclick="clearLogs()">清空日志</button>
            <button onclick="exportLogs()">导出日志</button>
            <input type="text" class="search-box" id="searchBox" placeholder="搜索日志内容..." onkeyup="searchLogs()">
        </div>

        <div class="log-output" id="logOutput">
            <div class="log-info">等待文件上传...</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        let allLogs = [];
        window.salesPriceData = null;
        window.basketPriceData = null;

        // 重写console.log来捕获所有日志
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.log = function(...args) {
            logToOutput('info', args.join(' '));
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            logToOutput('warning', args.join(' '));
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            logToOutput('error', args.join(' '));
            originalError.apply(console, args);
        };

        function logToOutput(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                type,
                message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message
            };
            
            allLogs.push(logEntry);
            
            const logOutput = document.getElementById('logOutput');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry log-${type}`;
            logDiv.innerHTML = `[${timestamp}] ${logEntry.message}`;
            
            logOutput.appendChild(logDiv);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 文件上传处理
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 菜篮子价格表文件上传处理
        const basketUploadArea = document.getElementById('basketUploadArea');
        const basketFileInput = document.getElementById('basketFileInput');

        basketUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            basketUploadArea.classList.add('dragover');
        });

        basketUploadArea.addEventListener('dragleave', () => {
            basketUploadArea.classList.remove('dragover');
        });

        basketUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            basketUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleBasketFile(files[0]);
            }
        });

        basketFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleBasketFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            console.log('📁 开始处理文件:', file.name);
            console.log('📊 文件大小:', file.size, 'bytes');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    console.log('🔄 开始解析Excel文件...');
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false,
                        raw: false,
                        defval: ''
                    });

                    console.log('📋 工作表名称:', workbook.SheetNames);
                    
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // 先尝试用数组格式解析，然后转换为对象格式
                    const rawDataArray = XLSX.utils.sheet_to_json(worksheet, {
                        header: 1,
                        defval: '',
                        blankrows: false,
                        raw: false
                    });

                    console.log('📊 数组格式原始数据前10行:', rawDataArray.slice(0, 10));

                    // 查找表头行
                    let headerRowIndex = -1;
                    for (let i = 0; i < Math.min(rawDataArray.length, 10); i++) {
                        const row = rawDataArray[i];
                        if (row && Array.isArray(row)) {
                            const hasProductField = row.some(cell =>
                                cell && typeof cell === 'string' &&
                                (cell.includes('物料名称') || cell.includes('商品名称') || cell.includes('产品名称'))
                            );
                            if (hasProductField) {
                                headerRowIndex = i;
                                console.log(`📋 找到表头行 ${i+1}:`, row);
                                break;
                            }
                        }
                    }

                    let rawData = [];
                    if (headerRowIndex >= 0) {
                        // 使用找到的表头行转换为对象格式
                        const headers = rawDataArray[headerRowIndex];
                        console.log('📋 使用表头:', headers);

                        for (let i = headerRowIndex + 1; i < rawDataArray.length; i++) {
                            const row = rawDataArray[i];
                            if (row && Array.isArray(row) && row.length > 0) {
                                const obj = {};
                                for (let j = 0; j < headers.length; j++) {
                                    if (headers[j] && headers[j].toString().trim() !== '') {
                                        const key = headers[j].toString().trim();
                                        const value = row[j] ? row[j].toString().trim() : '';
                                        obj[key] = value;
                                    }
                                }
                                // 只添加非空对象
                                if (Object.keys(obj).length > 0) {
                                    rawData.push(obj);
                                }
                            }
                        }

                        // 验证数据转换结果
                        console.log('📊 销售价格表数据验证:');
                        console.log('📊 总行数:', rawData.length);
                        console.log('📊 表头:', headers);
                        console.log('📊 前5行数据详细:');
                        rawData.slice(0, 5).forEach((item, index) => {
                            console.log(`📊 第${index + 1}行:`, JSON.stringify(item, null, 2));
                        });
                    } else {
                        // 如果没找到表头，使用默认方式解析
                        console.warn('⚠️ 未找到表头行，使用默认解析方式');
                        rawData = XLSX.utils.sheet_to_json(worksheet, {
                            defval: '',
                            blankrows: false
                        });
                    }

                    console.log('📊 原始数据总行数:', rawData.length);
                    console.log('🔍 前5行数据样本:');
                    rawData.slice(0, 5).forEach((item, index) => {
                        console.log(`🔍 第${index + 1}行:`, JSON.stringify(item, null, 2));
                    });

                    if (rawData.length > 0) {
                        const firstRow = rawData[0];
                        const columns = Object.keys(firstRow);
                        console.log('📝 可用列名:', columns);
                        
                        // 模拟筛选菜篮子部分的数据
                        filterBasketSalesData(rawData);
                    }

                } catch (error) {
                    console.error('❌ 文件解析失败:', error.message);
                }
            };
            
            reader.readAsArrayBuffer(file);
        }

        function filterBasketSalesData(salesData) {
            console.log('🔍 筛选菜篮子部分的销售数据...');
            console.log('原始数据总行数:', salesData.length);
            console.log('前5行数据样本:', salesData.slice(0, 5));

            // 先检查数据结构
            if (salesData.length > 0) {
                const firstRow = salesData[0];
                const columns = Object.keys(firstRow);
                console.log('可用列名:', columns);

                // 检查是否有市场价列，如果没有则使用执行价格
                const hasMarketPrice = columns.includes('市场价');
                const hasExecutionPrice = columns.includes('执行价格');
                console.log('是否有市场价列:', hasMarketPrice);
                console.log('是否有执行价格列:', hasExecutionPrice);

                if (!hasMarketPrice && !hasExecutionPrice) {
                    console.warn('⚠️ 未找到市场价或执行价格列');
                }
            }

            const basketData = [];
            let foundBasketSection = false;
            let basketSectionEnded = false;

            // 严格按照Excel文件中的分类标识
            const basketSectionStart = '菜篮子部分';

            // 可能的分类结束标识
            const sectionEndKeywords = [
                '市场部分', '蔬菜部分', '肉类部分', '水产部分', '粮油部分', '调料部分',
                '其他部分', '禽蛋部分', '奶制品部分', '豆制品部分', '面食类部分',
                '熟食类部分', '水果部分', '蔬菜', '肉类', '水产', '粮油', '调料',
                '其他', '禽蛋', '奶制品', '豆制品', '面食类', '熟食类', '水果', '市场'
            ];

            console.log('🔍 开始逐行扫描，寻找菜篮子部分...');

            for (let i = 0; i < salesData.length; i++) {
                const row = salesData[i];
                const rowText = Object.values(row).join(' ');

                // 详细日志：显示每一行的内容（前20行和菜篮子部分的所有行）
                if (i < 20 || foundBasketSection) {
                    console.log(`📋 行${i+1}内容: "${rowText}"`);
                    console.log(`📋 行${i+1}原始数据:`, JSON.stringify(row, null, 2));
                }

                // 如果找到"菜篮子部分"标识行
                if (rowText.includes(basketSectionStart) && !foundBasketSection) {
                    foundBasketSection = true;
                    basketSectionEnded = false;
                    console.log(`✅ 找到菜篮子部分起始行 ${i+1}:`, row);
                    console.log(`✅ 行内容: "${rowText}"`);
                    continue; // 跳过标题行
                }

                // 如果已经找到菜篮子部分，检查是否遇到新的分类标识
                if (foundBasketSection && !basketSectionEnded) {
                    const trimmedRowText = rowText.trim();

                    // 检查是否遇到其他分类标识
                    const isNewSection = sectionEndKeywords.some(keyword => {
                        return trimmedRowText === keyword ||
                               trimmedRowText.includes(keyword + '部分') ||
                               (trimmedRowText === keyword && keyword !== basketSectionStart);
                    });

                    if (isNewSection) {
                        basketSectionEnded = true;
                        console.log(`❌ 遇到新分类，菜篮子部分结束于行 ${i+1}: "${trimmedRowText}"`);
                        break;
                    }
                }

                // 检查是否是有效的数据行
                const hasProductName = row['物料名称'] && row['物料名称'].toString().trim() !== '';
                const hasUnit = row['计价单位'] && row['计价单位'].toString().trim() !== '';
                const marketPrice = row['市场价'];
                const executionPrice = row['执行价格'];
                const hasPrice = (marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '') ||
                               (executionPrice !== undefined && executionPrice !== null && executionPrice.toString().trim() !== '');

                // 详细日志：显示数据验证结果（只在菜篮子部分内）
                if (foundBasketSection && !basketSectionEnded) {
                    console.log(`🔍 行${i+1}数据验证:`, {
                        '原始行数据': row,
                        '物料名称': row['物料名称'],
                        'hasProductName': hasProductName,
                        '计价单位': row['计价单位'],
                        'hasUnit': hasUnit,
                        '市场价': marketPrice,
                        '执行价格': executionPrice,
                        'hasPrice': hasPrice,
                        '所有字段': Object.keys(row)
                    });
                }

                if (foundBasketSection && !basketSectionEnded && hasProductName && hasUnit && hasPrice) {
                    basketData.push(row);
                    const priceValue = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? marketPrice : executionPrice;
                    const priceField = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? '市场价' : '执行价格';

                    console.log(`✅ 收集菜篮子数据行 ${i+1}: 物料名称="${row['物料名称']}", 计价单位="${row['计价单位']}", ${priceField}="${priceValue}"`);
                }
            }

            console.log(`✅ 筛选完成，找到 ${basketData.length} 行菜篮子数据`);
            console.log('菜篮子部分数据行数:', basketData.length);
            console.log('是否找到菜篮子部分标识:', foundBasketSection);

            // 显示筛选结果汇总
            if (basketData.length > 0) {
                console.log('🥬 菜篮子数据汇总（前10行）:');
                basketData.slice(0, 10).forEach((item, index) => {
                    const marketPrice = item['市场价'];
                    const executionPrice = item['执行价格'];
                    const priceValue = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? marketPrice : executionPrice;
                    const priceField = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? '市场价' : '执行价格';

                    console.log(`🥬 ${index + 1}. ${item['物料名称']} | ${item['计价单位']} | ${priceField}: ${priceValue}`);
                });

                if (basketData.length > 10) {
                    console.log(`🥬 ... 还有 ${basketData.length - 10} 行数据`);
                }
            }

            // 如果找到了标识但没有数据，进行详细分析
            if (foundBasketSection && basketData.length === 0) {
                console.log('⚠️ 找到了菜篮子部分标识，但没有收集到有效数据');
                console.log('可能原因：');
                console.log('1. 菜篮子部分后面没有有效的数据行');
                console.log('2. 数据行缺少必要的字段（物料名称、计价单位、价格）');
                console.log('3. 数据格式不符合预期');
            }

            // 如果仍然没有找到数据，输出更详细的调试信息
            if (basketData.length === 0) {
                console.log('❌ 未找到菜篮子数据，进行详细分析...');
                console.log('检查前10行数据的物料名称:');
                for (let i = 0; i < Math.min(10, salesData.length); i++) {
                    const row = salesData[i];
                    console.log(`行${i+1}: 物料名称="${row['物料名称']}", 计价单位="${row['计价单位']}", 市场价="${row['市场价']}", 执行价格="${row['执行价格']}"`);
                }

                // 搜索包含"菜篮子"的所有行
                console.log('🔍 搜索包含"菜篮子"的所有行:');
                for (let i = 0; i < salesData.length; i++) {
                    const row = salesData[i];
                    const rowText = Object.values(row).join(' ');
                    if (rowText.includes('菜篮子')) {
                        console.log(`行${i+1}: "${rowText}"`);
                    }
                }
            }

            // 保存销售价目表数据到全局变量
            window.salesPriceData = basketData;
            console.log(`💾 销售价目表数据已保存，共 ${basketData.length} 行`);

            // 检查是否可以开始比价分析
            checkComparisonReady();

            return basketData;
        }

        function handleBasketFile(file) {
            console.log('🛒 开始处理菜篮子价格表文件:', file.name);
            console.log('📊 文件大小:', file.size, 'bytes');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    console.log('🔄 开始解析菜篮子价格表Excel文件...');
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false,
                        raw: false,
                        defval: ''
                    });

                    console.log('📋 菜篮子价格表工作表名称:', workbook.SheetNames);

                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // 解析菜篮子价格表数据
                    parseBasketPriceData(worksheet);

                } catch (error) {
                    console.error('❌ 菜篮子价格表文件解析失败:', error.message);
                }
            };

            reader.readAsArrayBuffer(file);
        }

        function parseBasketPriceData(worksheet) {
            console.log('🛒 开始解析菜篮子价格表数据...');

            // 先用数组格式解析
            const rawDataArray = XLSX.utils.sheet_to_json(worksheet, {
                header: 1,
                defval: '',
                raw: false
            });

            console.log('🛒 菜篮子价格表原始数据行数:', rawDataArray.length);
            console.log('🛒 前5行原始数据:');
            rawDataArray.slice(0, 5).forEach((row, index) => {
                console.log(`🛒 第${index + 1}行:`, row);
            });

            // 查找表头行 - 更灵活的识别逻辑
            let headerRowIndex = -1;
            let headers = [];

            console.log('🛒 开始查找菜篮子价格表表头行...');

            for (let i = 0; i < Math.min(10, rawDataArray.length); i++) {
                const row = rawDataArray[i];
                if (row && Array.isArray(row) && row.length > 0) {
                    const rowText = row.join('').toLowerCase();
                    console.log(`🛒 检查第${i + 1}行: "${rowText}"`);
                    console.log(`🛒 第${i + 1}行原始数据:`, row);

                    // 多种表头识别条件 - 根据README.md的格式要求
                    const hasProductName = rowText.includes('商品/分类名称') || rowText.includes('商品分类名称') ||
                                         rowText.includes('物料名称') || rowText.includes('商品名称') ||
                                         rowText.includes('品名') || rowText.includes('商品');
                    const hasUnit = rowText.includes('计价单位') || rowText.includes('单位');
                    const hasDate = rowText.includes('月') && rowText.includes('日');
                    const hasBenqi = rowText.includes('本期');

                    console.log(`🛒 第${i + 1}行检查结果: 商品名称=${hasProductName}, 单位=${hasUnit}, 日期=${hasDate}, 本期=${hasBenqi}`);

                    // 如果包含商品名称和单位，或者包含日期和本期，则认为是表头行
                    if ((hasProductName && hasUnit) || (hasDate && hasBenqi) ||
                        (hasProductName && hasDate) || (hasUnit && hasDate)) {
                        headerRowIndex = i;
                        headers = row.map(cell => cell ? cell.toString().trim() : '').filter(cell => cell !== '');
                        console.log(`🛒 找到菜篮子价格表表头行 ${i + 1}:`, headers);
                        break;
                    }
                }
            }

            if (headerRowIndex === -1) {
                console.error('❌ 未找到菜篮子价格表表头行');
                return;
            }

            // 查找日期列 - 处理多行表头结构
            console.log('🔍 开始查找日期列（多行表头结构）...');

            // 获取日期行和本期行
            let dateRowIndex = -1;
            let periodRowIndex = -1;
            let dateRow = [];
            let periodRow = [];

            // 查找包含日期的行（通常在表头行后1-3行内）
            for (let i = headerRowIndex + 1; i < Math.min(headerRowIndex + 4, rawDataArray.length); i++) {
                const row = rawDataArray[i];
                if (row && Array.isArray(row)) {
                    const rowText = row.join('').toLowerCase();
                    console.log(`🔍 检查第${i + 1}行: "${rowText}"`);

                    if (rowText.includes('2025年') && rowText.includes('月') && rowText.includes('日')) {
                        dateRowIndex = i;
                        dateRow = row;
                        console.log(`📅 找到日期行 ${i + 1}:`, dateRow);
                    }

                    if (rowText.includes('本期')) {
                        periodRowIndex = i;
                        periodRow = row;
                        console.log(`📊 找到本期行 ${i + 1}:`, periodRow);
                    }
                }
            }

            if (dateRowIndex === -1 || periodRowIndex === -1) {
                console.error('❌ 未找到日期行或本期行');
                return;
            }

            // 查找最佳日期列（优先级：25日 > 26日 > 27日 > 28日 > 29日）
            const datePriority = [
                {pattern: '2025年06月25日', name: '25日'},
                {pattern: '2025年06月26日', name: '26日'},
                {pattern: '2025年06月27日', name: '27日'},
                {pattern: '2025年06月28日', name: '28日'},
                {pattern: '2025年06月29日', name: '29日'}
            ];
            let selectedDateColumn = -1;
            let selectedDate = '';

            console.log('🎯 按优先级查找日期列...');
            for (const dateInfo of datePriority) {
                console.log(`🔍 查找日期: ${dateInfo.pattern}`);
                for (let col = 0; col < dateRow.length; col++) {
                    const dateCell = dateRow[col];
                    if (dateCell && dateCell.toString().includes(dateInfo.pattern)) {
                        console.log(`📅 在列${col + 1}找到日期: ${dateCell}`);

                        // 查找该日期对应的"本期"列（通常在日期列后1-2列内）
                        for (let offset = 0; offset <= 2; offset++) {
                            const checkCol = col + offset;
                            if (periodRow.length > checkCol && periodRow[checkCol] &&
                                periodRow[checkCol].toString().includes('本期')) {
                                selectedDateColumn = checkCol;
                                selectedDate = dateInfo.name;
                                console.log(`🎯 找到优先级日期列 ${checkCol + 1}: ${dateCell} -> ${periodRow[checkCol]} (${dateInfo.name})`);
                                break;
                            } else if (periodRow[checkCol]) {
                                console.log(`🔍 列${checkCol + 1}的本期行内容: ${periodRow[checkCol]}`);
                            }
                        }

                        if (selectedDateColumn !== -1) break;
                    }
                }
                if (selectedDateColumn !== -1) break;
            }

            // 如果没找到优先级日期，查找任何包含"本期"的日期列
            if (selectedDateColumn === -1) {
                console.warn('⚠️ 未找到符合优先级的日期列，尝试查找任何包含"本期"的日期列');
                for (let col = 0; col < periodRow.length; col++) {
                    const periodCell = periodRow[col];
                    if (periodCell && periodCell.toString().includes('本期')) {
                        // 检查对应位置是否有日期
                        if (dateRow.length > col && dateRow[col] &&
                            dateRow[col].toString().includes('月')) {
                            selectedDateColumn = col;
                            selectedDate = '本期';
                            console.log(`📅 找到日期列 ${col + 1}: ${dateRow[col]} -> ${periodCell}`);
                            break;
                        }
                    }
                }
            }

            if (selectedDateColumn === -1) {
                console.error('❌ 未找到有效的日期列');
                return;
            }

            // 转换为对象格式 - 从本期行之后开始解析数据
            const basketPriceData = [];
            const dataStartRow = Math.max(periodRowIndex + 1, headerRowIndex + 3); // 数据从本期行后开始

            console.log(`🛒 开始解析数据，从第${dataStartRow + 1}行开始...`);

            for (let i = dataStartRow; i < rawDataArray.length; i++) {
                const row = rawDataArray[i];
                if (row && Array.isArray(row) && row.length > 0) {
                    const obj = {};

                    // 使用基本表头构建对象
                    for (let j = 0; j < headers.length && j < row.length; j++) {
                        if (headers[j] && headers[j].toString().trim() !== '') {
                            const key = headers[j].toString().trim();
                            const value = row[j] ? row[j].toString().trim() : '';
                            obj[key] = value;
                        }
                    }

                    // 添加选中日期列的价格数据
                    if (selectedDateColumn !== -1 && row[selectedDateColumn]) {
                        obj['选中日期价格'] = row[selectedDateColumn].toString().trim();
                        obj['选中日期'] = selectedDate;
                    }

                    // 检查是否有有效的商品名称和价格
                    const productName = obj['商品/分类名称'] || obj['商品分类名称'] || obj['物料名称'] || '';
                    const unit = obj['计价单位'] || '';
                    const price = obj['选中日期价格'] || '';

                    if (productName && productName.trim() !== '' &&
                        unit && unit.trim() !== '' &&
                        price && price.toString().trim() !== '' &&
                        !isNaN(parseFloat(price))) {
                        basketPriceData.push(obj);
                    }
                }
            }

            console.log(`🛒 菜篮子价格表解析完成，找到 ${basketPriceData.length} 行有效数据`);
            console.log('🛒 使用的日期列:', selectedDateColumn);
            console.log('🛒 前10行菜篮子价格数据:');
            basketPriceData.slice(0, 10).forEach((item, index) => {
                const productName = item['商品/分类名称'] || item['商品分类名称'] || item['物料名称'] || '';
                const unit = item['计价单位'] || '';
                const price = item['选中日期价格'] || '';
                console.log(`🛒 ${index + 1}. ${productName} | ${unit} | ${selectedDateColumn}: ${price}`);
            });

            // 保存菜篮子价格表数据到全局变量
            window.basketPriceData = basketPriceData;
            console.log(`💾 菜篮子价格表数据已保存，共 ${basketPriceData.length} 行`);

            // 检查是否可以开始比价分析
            checkComparisonReady();

            return basketPriceData;
        }

        function clearLogs() {
            allLogs = [];
            document.getElementById('logOutput').innerHTML = '<div class="log-info">日志已清空</div>';
        }

        function exportLogs() {
            const logText = allLogs.map(log => `[${log.timestamp}] [${log.type.toUpperCase()}] ${log.message}`).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function searchLogs() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const logOutput = document.getElementById('logOutput');
            const logEntries = logOutput.querySelectorAll('.log-entry');

            logEntries.forEach(entry => {
                const text = entry.textContent.toLowerCase();
                if (searchTerm === '' || text.includes(searchTerm)) {
                    entry.style.display = 'block';
                    if (searchTerm !== '') {
                        entry.innerHTML = entry.innerHTML.replace(
                            new RegExp(`(${searchTerm})`, 'gi'),
                            '<span class="highlight">$1</span>'
                        );
                    }
                } else {
                    entry.style.display = 'none';
                }
            });
        }

        // 检查是否可以开始比价分析
        function checkComparisonReady() {
            const compareBtn = document.getElementById('compareBtn');
            if (window.salesPriceData && window.basketPriceData) {
                compareBtn.disabled = false;
                compareBtn.style.background = '#28a745';
                console.log('✅ 两个文件都已上传，可以开始比价分析');
            } else {
                compareBtn.disabled = true;
                compareBtn.style.background = '#6c757d';
                console.log('⏳ 等待文件上传完成...');
            }
        }

        // 开始比价分析
        function startComparison() {
            if (!window.salesPriceData || !window.basketPriceData) {
                console.error('❌ 请先上传两个文件');
                return;
            }

            console.log('🔍 开始比价分析...');
            console.log(`📊 销售价目表数据: ${window.salesPriceData.length} 行`);
            console.log(`🛒 菜篮子价格表数据: ${window.basketPriceData.length} 行`);

            const comparisonResults = performComparison(window.salesPriceData, window.basketPriceData);
            displayComparisonResults(comparisonResults);
        }

        // 执行比价分析
        function performComparison(salesData, basketData) {
            console.log('🔄 开始执行比价分析...');

            const results = {
                exactMatches: [],
                fuzzyMatches: [],
                noMatches: [],
                statistics: {
                    totalSalesItems: salesData.length,
                    totalBasketItems: basketData.length,
                    exactMatchCount: 0,
                    fuzzyMatchCount: 0,
                    noMatchCount: 0
                }
            };

            // 为每个销售价目表商品查找匹配
            salesData.forEach((salesItem, index) => {
                const salesName = salesItem['物料名称'] || '';
                const salesUnit = salesItem['计价单位'] || '';
                const salesPrice = parseFloat(salesItem['市场价'] || salesItem['执行价格'] || 0);

                console.log(`🔍 处理销售商品 ${index + 1}/${salesData.length}: ${salesName} (${salesUnit}) - ${salesPrice}`);

                let bestMatch = null;
                let matchType = 'none';
                let similarity = 0;

                // 查找精确匹配
                for (const basketItem of basketData) {
                    const basketName = basketItem['商品/分类名称'] || basketItem['商品分类名称'] || '';
                    const basketUnit = basketItem['计价单位'] || '';
                    const basketPrice = parseFloat(basketItem['选中日期价格'] || 0);

                    // 精确匹配：商品名称和单位都匹配（考虑单位转换）
                    if (salesName === basketName && isUnitCompatible(salesUnit, basketUnit)) {
                        bestMatch = {
                            basketItem,
                            basketName,
                            basketUnit,
                            basketPrice,
                            priceDiff: salesPrice - basketPrice,
                            priceDiffPercent: basketPrice > 0 ? ((salesPrice - basketPrice) / basketPrice * 100) : 0
                        };
                        matchType = 'exact';
                        similarity = 100;
                        break;
                    }
                }

                // 如果没有精确匹配，尝试模糊匹配
                if (!bestMatch) {
                    for (const basketItem of basketData) {
                        const basketName = basketItem['商品/分类名称'] || basketItem['商品分类名称'] || '';
                        const basketUnit = basketItem['计价单位'] || '';
                        const basketPrice = parseFloat(basketItem['选中日期价格'] || 0);

                        // 模糊匹配：单位匹配（考虑单位转换），商品名称相似度>70%
                        if (isUnitCompatible(salesUnit, basketUnit)) {
                            const nameSimilarity = calculateSimilarity(salesName, basketName);
                            if (nameSimilarity > 70 && nameSimilarity > similarity) {
                                bestMatch = {
                                    basketItem,
                                    basketName,
                                    basketUnit,
                                    basketPrice,
                                    priceDiff: salesPrice - basketPrice,
                                    priceDiffPercent: basketPrice > 0 ? ((salesPrice - basketPrice) / basketPrice * 100) : 0
                                };
                                matchType = 'fuzzy';
                                similarity = nameSimilarity;
                            }
                        }
                    }
                }

                // 记录结果
                const result = {
                    salesItem,
                    salesName,
                    salesUnit,
                    salesPrice,
                    matchType,
                    similarity,
                    ...bestMatch
                };

                if (matchType === 'exact') {
                    results.exactMatches.push(result);
                    results.statistics.exactMatchCount++;
                } else if (matchType === 'fuzzy') {
                    results.fuzzyMatches.push(result);
                    results.statistics.fuzzyMatchCount++;
                } else {
                    results.noMatches.push(result);
                    results.statistics.noMatchCount++;
                }
            });

            console.log('📊 比价分析完成');
            console.log(`✅ 精确匹配: ${results.statistics.exactMatchCount} 个`);
            console.log(`🟡 模糊匹配: ${results.statistics.fuzzyMatchCount} 个`);
            console.log(`❌ 未匹配: ${results.statistics.noMatchCount} 个`);

            return results;
        }

        // 检查单位是否兼容
        function isUnitCompatible(salesUnit, basketUnit) {
            // 直接匹配
            if (salesUnit === basketUnit) {
                return true;
            }

            // 单位转换映射
            const unitMappings = {
                '斤': ['元/500克', '500克', '元/斤'],
                '元/500克': ['斤', '500克', '元/斤'],
                '500克': ['斤', '元/500克', '元/斤'],
                '元/斤': ['斤', '元/500克', '500克'],
                '公斤': ['元/公斤', 'kg', '千克'],
                '元/公斤': ['公斤', 'kg', '千克'],
                'kg': ['公斤', '元/公斤', '千克'],
                '千克': ['公斤', '元/公斤', 'kg'],
                '桶': ['元/桶'],
                '元/桶': ['桶'],
                '个': ['元/个'],
                '元/个': ['个']
            };

            // 检查是否有映射关系
            if (unitMappings[salesUnit] && unitMappings[salesUnit].includes(basketUnit)) {
                return true;
            }

            if (unitMappings[basketUnit] && unitMappings[basketUnit].includes(salesUnit)) {
                return true;
            }

            return false;
        }

        // 计算字符串相似度
        function calculateSimilarity(str1, str2) {
            if (!str1 || !str2) return 0;

            const len1 = str1.length;
            const len2 = str2.length;
            const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

            for (let i = 0; i <= len1; i++) matrix[i][0] = i;
            for (let j = 0; j <= len2; j++) matrix[0][j] = j;

            for (let i = 1; i <= len1; i++) {
                for (let j = 1; j <= len2; j++) {
                    if (str1[i - 1] === str2[j - 1]) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j - 1] + 1
                        );
                    }
                }
            }

            const distance = matrix[len1][len2];
            const maxLen = Math.max(len1, len2);
            return maxLen > 0 ? ((maxLen - distance) / maxLen) * 100 : 0;
        }

        // 显示比价结果
        function displayComparisonResults(results) {
            console.log('📋 显示比价分析结果...');

            // 显示统计摘要
            console.log('📊 === 比价分析统计摘要 ===');
            console.log(`总销售商品数: ${results.statistics.totalSalesItems}`);
            console.log(`总菜篮子商品数: ${results.statistics.totalBasketItems}`);
            console.log(`精确匹配: ${results.statistics.exactMatchCount} (${(results.statistics.exactMatchCount/results.statistics.totalSalesItems*100).toFixed(1)}%)`);
            console.log(`模糊匹配: ${results.statistics.fuzzyMatchCount} (${(results.statistics.fuzzyMatchCount/results.statistics.totalSalesItems*100).toFixed(1)}%)`);
            console.log(`未匹配: ${results.statistics.noMatchCount} (${(results.statistics.noMatchCount/results.statistics.totalSalesItems*100).toFixed(1)}%)`);

            // 显示精确匹配结果
            if (results.exactMatches.length > 0) {
                console.log('🟢 === 精确匹配结果 ===');
                results.exactMatches.slice(0, 10).forEach((match, index) => {
                    const diffColor = match.priceDiff > 0 ? '🔴' : match.priceDiff < 0 ? '🟢' : '⚪';
                    console.log(`${diffColor} ${index + 1}. ${match.salesName} (${match.salesUnit})`);
                    console.log(`   销售价: ¥${match.salesPrice.toFixed(2)} | 菜篮子价: ¥${match.basketPrice.toFixed(2)} | 差价: ¥${match.priceDiff.toFixed(2)} (${match.priceDiffPercent.toFixed(1)}%)`);
                });
                if (results.exactMatches.length > 10) {
                    console.log(`   ... 还有 ${results.exactMatches.length - 10} 个精确匹配`);
                }
            }

            // 显示模糊匹配结果
            if (results.fuzzyMatches.length > 0) {
                console.log('🟡 === 模糊匹配结果 ===');
                results.fuzzyMatches.slice(0, 5).forEach((match, index) => {
                    const diffColor = match.priceDiff > 0 ? '🔴' : match.priceDiff < 0 ? '🟢' : '⚪';
                    console.log(`${diffColor} ${index + 1}. ${match.salesName} ≈ ${match.basketName} (相似度: ${match.similarity.toFixed(1)}%)`);
                    console.log(`   销售价: ¥${match.salesPrice.toFixed(2)} | 菜篮子价: ¥${match.basketPrice.toFixed(2)} | 差价: ¥${match.priceDiff.toFixed(2)} (${match.priceDiffPercent.toFixed(1)}%)`);
                });
                if (results.fuzzyMatches.length > 5) {
                    console.log(`   ... 还有 ${results.fuzzyMatches.length - 5} 个模糊匹配`);
                }
            }

            // 显示未匹配结果
            if (results.noMatches.length > 0) {
                console.log('❌ === 未匹配商品 ===');
                results.noMatches.slice(0, 5).forEach((item, index) => {
                    console.log(`❌ ${index + 1}. ${item.salesName} (${item.salesUnit}) - ¥${item.salesPrice.toFixed(2)}`);
                });
                if (results.noMatches.length > 5) {
                    console.log(`   ... 还有 ${results.noMatches.length - 5} 个未匹配商品`);
                }
            }

            console.log('✅ 比价分析完成！');
        }

        // 初始化日志
        console.log('🚀 日志调试工具已启动');
        console.log('📝 请上传销售价目表和菜篮子价格表Excel文件进行测试');
    </script>
</body>
</html>
